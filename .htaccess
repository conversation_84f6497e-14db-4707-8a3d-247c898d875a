<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

    <Files .env>
        Order allow,deny
        Deny from all
    </Files>
    
    # Enable allow_url_fopen
    php_value allow_url_fopen 1

</IfModule>

# Force PHP execution for .php files
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>

# php -- <PERSON><PERSON><PERSON> cPanel-generated handler, do not edit
# Set the “ea-php83” package as the default “PHP” programming language.
<IfModule mime_module>
  AddType application/x-httpd-php .php .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
