# MINIMAL AMPPS httpd.conf FIX
# Copy this content to C:\Ampps\apache\conf\httpd.conf

# IMPORTANT: Replace the PHP module loading section ONLY
# Find these lines in your AMPPS httpd.conf and replace them:

# REMOVE these lines if they exist:
# LoadModule php_module modules/libphp.so
# AddType application/x-httpd-php .php

# KEEP this line (should already exist):
LoadModule {$php_mod}_module "{$path}/{$php_dir}/{$php}apache2_4.dll"

# ENSURE these sections exist in your httpd.conf:

# 1. In the <IfModule mime_module> section, ensure this line exists:
AddType application/x-httpd-php .phtml .pwml .php5 .php4 .php3 .php2 .php .inc

# 2. In the <IfModule dir_module> section:
DirectoryIndex index.php index.html

# 3. Add this section after the mime_module section:
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>

# 4. In your main <Directory "{$path}/www"> section, add:
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>

# 5. In your <VirtualHost 127.0.0.1:80> <Directory "{$path}/www"> section, add:
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>
