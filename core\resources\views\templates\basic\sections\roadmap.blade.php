@php
    $roadmapContent = getContent('roadmap.content', true);
    $roadmapElement = getContent('roadmap.element', orderById: true);
@endphp

<!-- roadmap section start -->
<section class="pt-100 pb-100 bg_img overlay--one"
style="background-image: url('{{ frontendImage('roadmap', @$roadmapContent->data_values->image, '1800x1200') }}');"
data-paroller-factor="0.3"
>
<div class="container">
  <div class="row justify-content-center">
    <div class="col-lg-6 text-center">
      <div class="section-header">
        <h2 class="section-title">{{ __(@$roadmapContent->data_values->heading) }}</h2>
        <p class="mt-3">{{ __(@$roadmapContent->data_values->subheading) }}</p>
      </div>
    </div>
  </div><!-- row end -->
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="roadmap-wrapper">
        @foreach($roadmapElement as $map)
        <div class="single-roadmap wow  {{ $loop->odd ? 'fadeInLeft' : 'fadeInRight' }} " data-wow-duration="0.5s" data-wow-delay="0.3s">
            <div class="roadmap-dot"></div>
            <span class="roadmap-date">{{ __(@$map->data_values->date) }}</span>
            <h4 class="title">{{ __(@$map->data_values->title) }}</h4>
            <p>{{ __(@$map->data_values->text) }}</p>
        </div>
        @endforeach
      </div>
    </div>
  </div>
</div>
</section>
