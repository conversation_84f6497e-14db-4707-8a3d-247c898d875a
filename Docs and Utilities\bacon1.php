<?php
/**
 * Simple Crypto Payment UI Test with Bacon QR Code
 *
 * This file demonstrates the modern UI for crypto payments
 * with Bacon QR Code integration.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define the path to your project - using server-compatible paths
$projectPath = __DIR__ . '/';
$rootPath = dirname(dirname(__DIR__)) . '/';

// Enable debug mode to help find the correct paths
$debugMode = true;

// Define the exact paths to the libraries based on your server structure
$enumPath = $projectPath . 'core/vendor/dasprid/enum/src/';
$baconPath = $projectPath . 'core/vendor/bacon-qr-code/src/';

// Function to safely include a file
function safe_require_once($file) {
    if (file_exists($file)) {
        require_once $file;
        return true;
    } else {
        error_log("Warning: File not found: $file");
        return false;
    }
}

// Check if the DASPRiD Enum library files exist
$enumFilesExist = file_exists($enumPath . 'Enum.php');

// If the DASPRiD Enum library files don't exist, create a minimal implementation
if (!$enumFilesExist) {
    error_log("DASPRiD Enum library not found at $enumPath, creating minimal implementation");

    // Create a minimal implementation of the required classes
    if (!class_exists('\\DASPRiD\\Enum\\Exception\\ExceptionInterface')) {
        class_alias('Exception', '\\DASPRiD\\Enum\\Exception\\ExceptionInterface');
    }

    if (!class_exists('\\DASPRiD\\Enum\\AbstractEnum')) {
        eval('
        namespace DASPRiD\\Enum;

        abstract class AbstractEnum {
            private static $instances = [];
            private $name;

            protected function __construct($name) {
                $this->name = $name;
            }

            public static function valueOf($name) {
                $class = get_called_class();

                if (!isset(self::$instances[$class][$name])) {
                    $instance = new $class($name);
                    self::$instances[$class][$name] = $instance;
                }

                return self::$instances[$class][$name];
            }

            public function __toString() {
                return $this->name;
            }
        }
        ');
    }

    if (!class_exists('\\DASPRiD\\Enum\\Enum')) {
        eval('
        namespace DASPRiD\\Enum;

        class Enum extends AbstractEnum {
        }
        ');
    }

    if (!class_exists('\\DASPRiD\\Enum\\EnumMap')) {
        eval('
        namespace DASPRiD\\Enum;

        class EnumMap {
            private $map = [];

            public function __construct() {
            }

            public function put($key, $value) {
                $this->map[(string)$key] = $value;
                return $this;
            }

            public function get($key) {
                return $this->map[(string)$key] ?? null;
            }
        }
        ');
    }
} else {
    // If the DASPRiD Enum library files exist, include them
    safe_require_once($enumPath . 'Exception/ExceptionInterface.php');
    safe_require_once($enumPath . 'Exception/CloneNotSupportedException.php');
    safe_require_once($enumPath . 'Exception/IllegalArgumentException.php');
    safe_require_once($enumPath . 'Exception/MismatchException.php');
    safe_require_once($enumPath . 'Exception/SerializeNotSupportedException.php');
    safe_require_once($enumPath . 'Exception/UnserializeNotSupportedException.php');
    safe_require_once($enumPath . 'AbstractEnum.php');
    safe_require_once($enumPath . 'Enum.php');
    safe_require_once($enumPath . 'EnumMap.php');
    safe_require_once($enumPath . 'EnumSet.php');
}

// We're using the exact paths defined above
$baconAvailable = file_exists($baconPath . 'Writer.php');
$enumAvailable = file_exists($enumPath . 'AbstractEnum.php');

// Log the paths for debugging
error_log("Bacon QR Code path: $baconPath - " . ($baconAvailable ? "Found" : "Not Found"));
error_log("DASPRiD Enum path: $enumPath - " . ($enumAvailable ? "Found" : "Not Found"));

// For debug information
$checkedBaconPaths = [$baconPath];
$checkedEnumPaths = [$enumPath];

// Function to check if a file exists and log the result
function checkFileExists($path) {
    $exists = file_exists($path);
    error_log("Checking file: $path - " . ($exists ? "Found" : "Not Found"));
    return $exists;
}

// Debug information
$debugInfo = '';
if ($debugMode) {
    $debugInfo .= '<div style="background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px;">';
    $debugInfo .= '<h4>Debug Information</h4>';
    $debugInfo .= '<p>Current directory: ' . __DIR__ . '</p>';
    $debugInfo .= '<p>Root directory: ' . $rootPath . '</p>';

    // Directory listing for core/vendor
    $coreVendorPath = $projectPath . 'core/vendor/';
    $debugInfo .= '<h5>Directory Listing for ' . $coreVendorPath . ':</h5>';
    $debugInfo .= '<ul>';
    if (is_dir($coreVendorPath)) {
        $dirs = scandir($coreVendorPath);
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..') {
                $debugInfo .= '<li>' . $dir . ' - <span style="color: green;">Directory</span></li>';
            }
        }
    } else {
        $debugInfo .= '<li>Directory does not exist or is not accessible</li>';
    }
    $debugInfo .= '</ul>';

    // Check for bacon-qr-code directory
    $baconDirPath = $projectPath . 'core/vendor/bacon-qr-code/';
    $debugInfo .= '<h5>Directory Listing for ' . $baconDirPath . ':</h5>';
    $debugInfo .= '<ul>';
    if (is_dir($baconDirPath)) {
        $dirs = scandir($baconDirPath);
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..') {
                $debugInfo .= '<li>' . $dir . '</li>';
            }
        }
    } else {
        $debugInfo .= '<li>Directory does not exist or is not accessible</li>';
    }
    $debugInfo .= '</ul>';

    // Check for dasprid directory
    $daspridPath = $projectPath . 'core/vendor/dasprid/';
    $debugInfo .= '<h5>Directory Listing for ' . $daspridPath . ':</h5>';
    $debugInfo .= '<ul>';
    if (is_dir($daspridPath)) {
        $dirs = scandir($daspridPath);
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..') {
                $debugInfo .= '<li>' . $dir . '</li>';

                // If this is the enum directory, check its contents
                if ($dir == 'enum') {
                    $enumDirPath = $daspridPath . 'enum/';
                    $debugInfo .= '<ul>';
                    if (is_dir($enumDirPath)) {
                        $enumDirs = scandir($enumDirPath);
                        foreach ($enumDirs as $enumDir) {
                            if ($enumDir != '.' && $enumDir != '..') {
                                $debugInfo .= '<li>' . $enumDir . '</li>';

                                // If this is the src directory, check its contents
                                if ($enumDir == 'src') {
                                    $srcPath = $enumDirPath . 'src/';
                                    $debugInfo .= '<ul>';
                                    if (is_dir($srcPath)) {
                                        $srcFiles = scandir($srcPath);
                                        foreach ($srcFiles as $srcFile) {
                                            if ($srcFile != '.' && $srcFile != '..') {
                                                $debugInfo .= '<li>' . $srcFile . '</li>';
                                            }
                                        }
                                    }
                                    $debugInfo .= '</ul>';
                                }
                            }
                        }
                    }
                    $debugInfo .= '</ul>';
                }
            }
        }
    } else {
        $debugInfo .= '<li>Directory does not exist or is not accessible</li>';
    }
    $debugInfo .= '</ul>';

    $debugInfo .= '<h5>Checked Bacon QR Code Paths:</h5>';
    $debugInfo .= '<ul>';
    foreach ($checkedBaconPaths as $path) {
        $exists1 = file_exists($path . 'Writer.php');
        $exists2 = file_exists($path . '/Writer.php');
        $exists3 = file_exists($path . 'BaconQrCode/Writer.php');
        $exists4 = file_exists($path . '/BaconQrCode/Writer.php');

        $status = $exists1 ? '<span style="color: green;">Found</span>' :
                 ($exists2 ? '<span style="color: green;">Found with trailing slash</span>' :
                 ($exists3 ? '<span style="color: green;">Found in BaconQrCode subdirectory</span>' :
                 ($exists4 ? '<span style="color: green;">Found in BaconQrCode subdirectory with trailing slash</span>' :
                 '<span style="color: red;">Not Found</span>')));

        $debugInfo .= '<li>' . $path . ' - ' . $status . '</li>';
    }
    $debugInfo .= '</ul>';

    $debugInfo .= '<h5>Checked DASPRiD Enum Paths:</h5>';
    $debugInfo .= '<ul>';
    foreach ($checkedEnumPaths as $path) {
        $exists1 = file_exists($path . 'Enum.php');
        $exists2 = file_exists($path . '/Enum.php');
        $exists3 = file_exists($path . 'DASPRiD/Enum.php');
        $exists4 = file_exists($path . '/DASPRiD/Enum.php');

        $status = $exists1 ? '<span style="color: green;">Found</span>' :
                 ($exists2 ? '<span style="color: green;">Found with trailing slash</span>' :
                 ($exists3 ? '<span style="color: green;">Found in DASPRiD subdirectory</span>' :
                 ($exists4 ? '<span style="color: green;">Found in DASPRiD subdirectory with trailing slash</span>' :
                 '<span style="color: red;">Not Found</span>')));

        $debugInfo .= '<li>' . $path . ' - ' . $status . '</li>';
    }
    $debugInfo .= '</ul>';

    if ($baconPath) {
        $debugInfo .= '<h5>Bacon QR Code Files Found:</h5>';
        $debugInfo .= '<ul>';
        $files = [
            'Writer.php',
            'Common/BitArray.php',
            'Renderer/PlainTextRenderer.php',
            'Renderer/Image/SvgImageBackEnd.php',
            'Renderer/ImageRenderer.php'
        ];
        foreach ($files as $file) {
            $exists = file_exists($baconPath . $file);
            $debugInfo .= '<li>' . $baconPath . $file . ' - ' . ($exists ? '<span style="color: green;">Found</span>' : '<span style="color: red;">Not Found</span>') . '</li>';
        }
        $debugInfo .= '</ul>';
    }

    if ($enumPath) {
        $debugInfo .= '<h5>DASPRiD Enum Files Found:</h5>';
        $debugInfo .= '<ul>';
        $files = [
            'Enum.php',
            'AbstractEnum.php',
            'EnumMap.php',
            'EnumSet.php'
        ];
        foreach ($files as $file) {
            $exists = file_exists($enumPath . $file);
            $debugInfo .= '<li>' . $enumPath . $file . ' - ' . ($exists ? '<span style="color: green;">Found</span>' : '<span style="color: red;">Not Found</span>') . '</li>';
        }
        $debugInfo .= '</ul>';
    }

    $debugInfo .= '</div>';
}

// Function to safely include a file
function safe_require_once($file) {
    if (file_exists($file)) {
        require_once $file;
        return true;
    } else {
        echo "<!-- Warning: File not found: $file -->";
        return false;
    }
}

// Initialize flags
$enumFilesLoaded = true;
$baconFilesLoaded = true;

// Now include the Bacon QR Code library files
try {
    // Common files
    safe_require_once($baconPath . 'Common/BitArray.php');
    safe_require_once($baconPath . 'Common/BitMatrix.php');
    safe_require_once($baconPath . 'Common/BitUtils.php');
    safe_require_once($baconPath . 'Common/CharacterSetEci.php');
    safe_require_once($baconPath . 'Common/EcBlock.php');
    safe_require_once($baconPath . 'Common/EcBlocks.php');
    safe_require_once($baconPath . 'Common/ErrorCorrectionLevel.php');
    safe_require_once($baconPath . 'Common/FormatInformation.php');
    safe_require_once($baconPath . 'Common/Mode.php');
    safe_require_once($baconPath . 'Common/ReedSolomonCodec.php');
    safe_require_once($baconPath . 'Common/Version.php');

    // Encoder files
    safe_require_once($baconPath . 'Encoder/BlockPair.php');
    safe_require_once($baconPath . 'Encoder/ByteMatrix.php');
    safe_require_once($baconPath . 'Encoder/Encoder.php');
    safe_require_once($baconPath . 'Encoder/MaskUtil.php');
    safe_require_once($baconPath . 'Encoder/MatrixUtil.php');
    safe_require_once($baconPath . 'Encoder/QrCode.php');

    // Exception files
    safe_require_once($baconPath . 'Exception/ExceptionInterface.php');
    safe_require_once($baconPath . 'Exception/InvalidArgumentException.php');
    safe_require_once($baconPath . 'Exception/OutOfBoundsException.php');
    safe_require_once($baconPath . 'Exception/RuntimeException.php');
    safe_require_once($baconPath . 'Exception/UnexpectedValueException.php');
    safe_require_once($baconPath . 'Exception/WriterException.php');

    // Renderer files
    safe_require_once($baconPath . 'Renderer/RendererInterface.php');
    safe_require_once($baconPath . 'Renderer/PlainTextRenderer.php');
    safe_require_once($baconPath . 'Renderer/RendererStyle/RendererStyle.php');
    safe_require_once($baconPath . 'Renderer/Image/ImageBackEndInterface.php');
    safe_require_once($baconPath . 'Renderer/Image/SvgImageBackEnd.php');
    safe_require_once($baconPath . 'Renderer/ImageRenderer.php');
    safe_require_once($baconPath . 'Writer.php');
} catch (Exception $e) {
    error_log("Error loading Bacon QR Code library: " . $e->getMessage());
    $baconFilesLoaded = false;
}



// Function to generate QR code using Bacon QR Code library
function generateQrCode($data, $size = 240) {
    global $baconFilesLoaded, $enumFilesLoaded, $baconPath, $enumPath, $debugMode;

    // Debug message about which method is being used
    $debugMsg = '';

    try {
        // Try to use the SVG renderer first
        $debugMsg = '<!-- Using Bacon QR Code SVG renderer -->';
        error_log("Attempting to use Bacon QR Code SVG renderer");

        // Create a renderer style
        $rendererStyle = new \BaconQrCode\Renderer\RendererStyle\RendererStyle($size);

        // Create an SVG image backend
        $imageBackEnd = new \BaconQrCode\Renderer\Image\SvgImageBackEnd();

        // Create an image renderer
        $renderer = new \BaconQrCode\Renderer\ImageRenderer($rendererStyle, $imageBackEnd);

        // Create a writer
        $writer = new \BaconQrCode\Writer($renderer);

        // Generate a QR code
        $qrCode = $writer->writeString($data);

        return '<div style="text-align: center;">' . $qrCode . $debugMsg . '</div>';
    } catch (\Exception $e) {
        // Log the error
        $errorMsg = 'SVG QR Code generation failed: ' . $e->getMessage();
        error_log($errorMsg);

        // If SVG renderer fails, fallback to PlainTextRenderer
        try {
            $debugMsg = '<!-- Using Bacon QR Code PlainText renderer -->';
            error_log("Attempting to use Bacon QR Code PlainText renderer");

            $renderer = new \BaconQrCode\Renderer\PlainTextRenderer();
            $writer = new \BaconQrCode\Writer($renderer);

            // Generate a QR code
            $qrCode = $writer->writeString($data);

            // Convert the text QR code to an HTML table for better display
            $lines = explode("\n", $qrCode);
            $html = '<table style="border-collapse: collapse; line-height: 1; margin: 0 auto;">';
            foreach ($lines as $line) {
                $html .= '<tr>';
                for ($i = 0; $i < strlen($line); $i++) {
                    $char = $line[$i];
                    $color = ($char === '█') ? 'black' : 'white';
                    $html .= '<td style="width: 5px; height: 5px; background-color: ' . $color . ';"></td>';
                }
                $html .= '</tr>';
            }
            $html .= '</table>';

            return '<div style="text-align: center;">' . $html . $debugMsg . '</div>';
        } catch (\Exception $e2) {
            // Log the error
            $errorMsg = 'PlainText QR Code generation failed: ' . $e2->getMessage();
            error_log($errorMsg);

            // If all Bacon QR Code methods fail, use external API as last resort
            $debugMsg = '<!-- Fallback to external API because Bacon QR Code generation failed: ' . $errorMsg . ' -->';
            error_log("Falling back to external API for QR code generation");

            return '<div style="text-align: center;">
                <img src="https://api.qrserver.com/v1/create-qr-code/?data=' . urlencode($data) . '&size=' . $size . 'x' . $size . '" alt="QR Code" style="max-width: ' . $size . 'px;">
                ' . $debugMsg . '
            </div>';
        }
    }
}

// Test data
$amount = '0.001';
$currency = 'BTC';
$address = '**********************************'; // Test Bitcoin address
$order_id = 'TEST' . time();
$usd_amount = '50.00';
$expiry_minutes = 30;

// Generate QR code
$qrCode = generateQrCode("bitcoin:{$address}?amount={$amount}");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Crypto Payment Test Page">
    <title>Crypto Payment Test with Bacon QR Code</title>

    <!-- Diagnostic information (hidden in production) -->
    <script>
        console.log('Bacon QR Code Path: <?php echo $baconPath; ?>');
        console.log('DASPRiD Enum Path: <?php echo $enumPath; ?>');
        console.log('Bacon Files Loaded: <?php echo $baconFilesLoaded ? 'Yes' : 'No'; ?>');
        console.log('Enum Files Loaded: <?php echo $enumFilesLoaded ? 'Yes' : 'No'; ?>');
    </script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS for Payment Box -->
    <style>
        html {
            font-size: 16.8px; /* 14px increased by 20% */
        }
        @media (min-width: 768px) {
            html {
                font-size: 19.2px; /* 16px increased by 20% */
            }
            .tooltip-inner {
                max-width: 350px;
            }
        }
        .payment-container {
            max-width: 980px;
            margin: 0 auto;
        }
        .box-shadow {
            box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
        }
        .payment-box {
            border: 1px solid #d1d9e6;
            border-radius: 1rem;
            padding: 1.8rem; /* 1.5rem increased by 20% */
            margin-bottom: 2.4rem; /* 2rem increased by 20% */
            background-color: #fff;
            box-shadow:
                0.5rem 0.5rem 1rem rgba(0, 0, 0, 0.1),
                -0.5rem -0.5rem 1rem rgba(255, 255, 255, 0.8),
                inset 0 0 0 rgba(255, 255, 255, 0.8),
                inset 0 0 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container payment-container pt-3 pb-5">
        <?php if ($debugMode): ?>
            <?php echo $debugInfo; ?>
        <?php endif; ?>
        <div class="row">
            <div class="col-md-12">
                <div class="payment-box">
                    <div class="payment-header">
                        <h2 class="payment-title">Bitcoin Payment</h2>
                        <p class="text-muted">Order #<?php echo $order_id; ?></p>
                    </div>

                    <div class="payment-amount">
                        <div class="d-flex justify-content-between align-items-center">
                            <div style="font-size: 2.4rem; font-weight: 700;">
                                <span id="crypto-amount"><?php echo $amount; ?></span> <span id="crypto-currency"><?php echo $currency; ?></span>
                            </div>
                            <div style="font-size: 2.4rem; font-weight: 700;">
                                ≈ $<?php echo $usd_amount; ?> USD
                            </div>
                        </div>
                    </div>

                    <div class="payment-instructions" style="background-color: #e9f7fe; color: #0c5460; padding: 1.5rem; border-radius: 0.8rem; margin-bottom: 1.5rem; position: relative; overflow: hidden; border: 1px solid rgba(12, 84, 96, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(12, 84, 96, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                        <!-- Gradient overlay for 3D effect -->
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                        <p style="font-size: 1.5rem; margin-bottom: 1rem; position: relative;">Send exactly <strong style="font-size: 1.7rem;"><span id="crypto-amount-repeat"><?php echo $amount; ?></span> <span id="crypto-currency-repeat"><?php echo $currency; ?></span></strong> to this address:</p>
                    </div>

                    <div class="payment-address" style="font-family: monospace; font-size: 1.32rem; word-break: break-all; margin-bottom: 1rem; padding: 0.8rem; background-color: #f8f9fa; border-radius: 0.5rem; box-shadow: inset 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.05), inset -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.7); border: 1px solid rgba(0, 0, 0, 0.05);">
                        <div class="d-flex justify-content-between align-items-center">
                            <span id="crypto-address"><?php echo $address; ?></span>
                            <button class="btn btn-outline-secondary btn-copy" data-clipboard-target="#crypto-address" title="Copy Address" style="padding: 0.5rem 1rem;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- New layout with QR code on left and status on right -->
                    <div class="row">
                        <div class="col-md-5">
                            <!-- QR Code -->
                            <div id="qrcode" style="text-align: center; margin-bottom: 1.5rem;">
                                <?php echo $qrCode; ?>
                            </div>
                            <p style="font-size: 1.2rem; color: #0c5460; font-weight: 500; text-align: center;">Scan with your wallet app</p>
                        </div>
                        <div class="col-md-7">
                            <!-- Payment status -->
                            <div class="payment-status status-waiting" style="padding: 1.2rem; border-radius: 0.8rem; margin-bottom: 1.5rem; background-color: #fff3cd; color: #856404; box-shadow: 0.2rem 0.2rem 0.5rem rgba(133, 100, 4, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5); position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.05);">
                                <!-- Gradient overlay for 3D effect -->
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                                <div class="d-flex align-items-center" style="position: relative;">
                                    <i class="fas fa-clock me-3" style="font-size: 1.8rem;"></i>
                                    <div>
                                        <strong style="font-size: 1.44rem; display: block; margin-bottom: 0.5rem;">Waiting for payment</strong>
                                        <div style="font-size: 1.2rem;" id="status-message">Please send the exact amount to the address shown above</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Timer -->
                            <div class="text-center">
                                <div style="font-size: 1.2rem; color: #856404; font-weight: 500;">Payment expires in</div>
                                <div class="timer" id="payment-timer" style="font-size: 2.2rem;"><?php echo $expiry_minutes; ?>:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-3 mt-4">
                        <button class="btn btn-outline-secondary btn-lg py-3" id="btn-check-payment" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-sync-alt me-2"></i> Check Payment Status</span>
                        </button>
                        <a href="https://www.blockchain.com/explorer/addresses/btc/<?php echo $address; ?>" target="_blank" class="btn btn-primary btn-lg py-3" id="btn-open-explorer" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-search me-2"></i> Open in Blockchain Explorer</span>
                        </a>
                    </div>

                    <div class="payment-footer" style="border-top: 1px solid #e9ecef; padding-top: 1rem; margin-top: 1.5rem; font-size: 1.08rem; color: #6c757d;">
                        <div class="row">
                            <div class="col-md-6">
                                <small>Transaction ID: <span id="transaction-id">N/A</span></small>
                            </div>
                            <div class="col-md-6 text-end">
                                <small>Powered by Your Company</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Received Box (initially hidden) -->
                <div class="payment-box d-none" id="payment-received-box">
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                        <h2 class="mt-4" style="font-size: 2.4rem;">Payment Received!</h2>
                        <p class="lead" style="font-size: 1.44rem;">Thank you for your payment.</p>
                        <p style="font-size: 1.2rem;">Transaction ID: <span id="success-transaction-id">N/A</span></p>
                        <div class="mt-5">
                            <a href="#" class="btn btn-success btn-lg py-3 px-5" style="font-size: 1.2rem;">Continue to Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>

    <!-- Custom JavaScript for Payment Box -->
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            try {
                $('[data-bs-toggle="tooltip"]').tooltip();
            } catch (e) {
                console.warn('Bootstrap tooltip initialization failed:', e);
            }

            // Initialize clipboard.js
            try {
                var clipboard = new ClipboardJS('.btn-copy');

                clipboard.on('success', function(e) {
                    // Show a simple alert if tooltip fails
                    try {
                        $(e.trigger).tooltip({
                            title: 'Copied!',
                            placement: 'top',
                            trigger: 'manual'
                        }).tooltip('show');

                        setTimeout(function() {
                            $(e.trigger).tooltip('hide');
                        }, 1000);
                    } catch (tooltipError) {
                        alert('Address copied to clipboard!');
                    }

                    e.clearSelection();
                });

                clipboard.on('error', function(e) {
                    console.error('Clipboard copy failed:', e);
                    alert('Failed to copy. Please select and copy the address manually.');
                });
            } catch (e) {
                console.error('Clipboard.js initialization failed:', e);
                // Fallback for clipboard functionality
                $('.btn-copy').on('click', function() {
                    var text = $(this).data('clipboard-text');
                    prompt('Copy this address manually:', text);
                });
            }

            // Check payment status button
            $('#btn-check-payment').click(function() {
                var $btn = $(this);
                $btn.prop('disabled', true);
                $btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Checking...');

                // Simulate checking payment status
                setTimeout(function() {
                    $btn.prop('disabled', false);
                    $btn.html('<i class="fas fa-sync-alt me-2"></i> Check Payment Status');

                    // For demo purposes, randomly show payment received
                    if (Math.random() > 0.7) {
                        showPaymentReceived();
                    }
                }, 2000);
            });

            // Payment timer
            var timeLeft = <?php echo $expiry_minutes; ?> * 60; // Convert minutes to seconds
            var timerInterval = setInterval(function() {
                timeLeft--;

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    showPaymentExpired();
                } else {
                    var minutes = Math.floor(timeLeft / 60);
                    var seconds = timeLeft % 60;
                    $('#payment-timer').text(
                        (minutes < 10 ? '0' : '') + minutes + ':' +
                        (seconds < 10 ? '0' : '') + seconds
                    );
                }
            }, 1000);

            // Function to show payment received
            function showPaymentReceived() {
                $('.payment-box').first().addClass('d-none');
                $('#payment-received-box').removeClass('d-none');
                $('#success-transaction-id').text('txid_' + Math.random().toString(36).substr(2, 9));
                clearInterval(timerInterval);
            }

            // Function to show payment expired
            function showPaymentExpired() {
                $('.payment-status')
                    .removeClass('status-waiting')
                    .addClass('status-expired')
                    .css('background-color', '#f8d7da')
                    .css('color', '#721c24')
                    .html('<div class="d-flex align-items-center"><i class="fas fa-exclamation-circle me-3" style="font-size: 1.8rem;"></i><div><strong style="font-size: 1.44rem; display: block; margin-bottom: 0.5rem;">Payment Expired</strong><div style="font-size: 1.2rem;">The payment time has expired. Please start over.</div></div></div>');

                $('#btn-check-payment, #btn-open-explorer').prop('disabled', true);
            }
        });
    </script>
</body>
</html>
