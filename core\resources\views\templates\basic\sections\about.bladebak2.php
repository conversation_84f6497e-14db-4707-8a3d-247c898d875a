@php
    $aboutContent = getContent('about.content', true);
    $aboutElement = getContent('about.element', orderById: true);
@endphp

<div class="about-section pt-100 pb-100 bg_img overlay--one"
    style="background-image: url('{{ frontendImage('about', @$aboutContent->data_values->background_image, '1920x1280') }} ');">
    <div class="container">
        <div class="row align-items-center flex-wrap-reverse">
            <div class="col-lg-6 pe-xl-5 ">
                <div class="about-thumb mx-lg-0 mx-md-5">
                    <img src="{{ frontendImage('about', @$aboutContent->data_values->image, '600x515') }}">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content">
                    <div class="section-heading style-two">
                        <h2 class="section-title mb-3">{{ __(@$aboutContent->data_values->subheading) }}</h2>
                        <h3 class="section-heading__title mb-4"> {{ __(@$aboutContent->data_values->heading) }}</h3>
                        <p class="section-heading__desc mb-4"> {{ __(@$aboutContent->data_values->content) }}</p>
                    </div>
                    <ul class="text-list">
                        @foreach ($aboutElement as $item)
                            <li class="text-list__item">
                                <span class="text-list__item-icon"> @php echo @$item->data_values->icon; @endphp</span>
                                {{ __(@$item->data_values->title) }}
                            </li>
                        @endforeach
                    </ul>
                    <div class="about-content__button mt-4">
                        {{-- Commenting out the Explore Platform button
                        <a href="{{ @$aboutContent->data_values->button_link }}" class="btn btn--base">
                            {{ __(@$aboutContent->data_values->button_name) }}
                        </a>
                        --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

