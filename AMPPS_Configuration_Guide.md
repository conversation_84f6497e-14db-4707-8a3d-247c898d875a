# AMPPS Apache Configuration Guide for PHP Support

## Problem
Browser downloads PHP files instead of executing them, indicating Apache is not properly configured to handle PHP.

## Solution Steps

### 1. Backup Current Configuration
Before making changes, backup your current AMPPS configuration:
```
Copy: C:\Ampps\apache\conf\httpd.conf
To: C:\Ampps\apache\conf\httpd.conf.backup
```

### 2. Open AMPPS httpd.conf File
Navigate to: `C:\Ampps\apache\conf\httpd.conf`
Open with a text editor (Notepad++, VS Code, etc.)

### 3. Verify/Add Required Modules
Find and ensure these modules are loaded (uncommented):

```apache
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule mime_module modules/mod_mime.so
LoadModule dir_module modules/mod_dir.so
```

### 4. Add PHP Module Loading
Find the section with other LoadModule directives and add:

```apache
# PHP Module - adjust path based on your AMPPS version
LoadModule php_module "C:/Ampps/php/libphp.so"
```

**Note:** If the above doesn't work, try:
```apache
LoadModule php8_module "C:/Ampps/php/php8apache2_4.dll"
```

### 5. Configure PHP MIME Types
Find the `<IfModule mime_module>` section and add:

```apache
<IfModule mime_module>
    # Existing MIME types...
    
    # Add PHP MIME types
    AddType application/x-httpd-php .php .phtml .php3 .php4 .php5
    AddType application/x-httpd-php-source .phps
</IfModule>
```

### 6. Set PHP INI Directory
Add this section after the LoadModule directives:

```apache
<IfModule php_module>
    PHPIniDir "C:/Ampps/php"
</IfModule>
```

### 7. Configure Directory Index
Find the `<IfModule dir_module>` section and ensure it includes:

```apache
<IfModule dir_module>
    DirectoryIndex index.php index.html index.htm
</IfModule>
```

### 8. Update Document Root Configuration
Find your DocumentRoot section and ensure it looks like:

```apache
DocumentRoot "C:/Ampps/www"

<Directory "C:/Ampps/www">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
    
    # PHP file handling
    <FilesMatch "\.php$">
        SetHandler application/x-httpd-php
    </FilesMatch>
</Directory>
```

### 9. Add Global PHP Handler
Add this at the end of the httpd.conf file:

```apache
# Global PHP file handler
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>
```

### 10. Restart AMPPS
1. Stop Apache in AMPPS Control Panel
2. Wait 5 seconds
3. Start Apache in AMPPS Control Panel
4. Check that Apache starts without errors

### 11. Test Configuration
1. Open browser
2. Go to: `http://localhost/phpinfo.php`
3. Should display "PHP is working!" instead of downloading

### 12. Test FocLabs Application
1. Go to: `http://localhost/`
2. Should display the FocLabs application

## Troubleshooting

### If Apache Won't Start:
1. Check AMPPS error logs: `C:\Ampps\apache\logs\error.log`
2. Verify PHP module path is correct
3. Check for syntax errors in httpd.conf

### If PHP Still Downloads:
1. Verify PHP module is loaded: Check `http://localhost/phpinfo.php`
2. Check .htaccess file in project root
3. Ensure AMPPS PHP service is running

### Common PHP Module Paths in AMPPS:
- `C:/Ampps/php/libphp.so`
- `C:/Ampps/php/php8apache2_4.dll`
- `C:/Ampps/php-8.2/libphp.so`

### Alternative: Use AMPPS Built-in Configuration
If manual configuration fails:
1. In AMPPS Control Panel
2. Go to PHP tab
3. Select correct PHP version
4. Click "Apply" or "Restart Services"

## Files Modified
- `C:\Ampps\apache\conf\httpd.conf` (main Apache configuration)
- `.htaccess` in project root (already updated)

## Expected Result
After these changes, PHP files should execute properly and display web pages instead of downloading as files.
