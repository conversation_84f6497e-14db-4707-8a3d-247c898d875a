That is the one by one<PERSON><PERSON><?php

namespace App\Services;

use libphonenumber\PhoneNumberUtil;
use libphonenumber\PhoneNumberFormat;

class PhoneNumberService
{
    private $phoneUtil;

    public function __construct()
    {
        $this->phoneUtil = PhoneNumberUtil::getInstance();
    }

    public function formatInternational($number, $countryCode)
    {
        try {
            $numberProto = $this->phoneUtil->parse($number, $countryCode);
            if ($this->phoneUtil->isValidNumber($numberProto)) {
                return $this->phoneUtil->format($numberProto, PhoneNumberFormat::E164);
            }
        } catch (\Exception $e) {
            \Log::error('Phone number formatting error: ' . $e->getMessage());
        }
        return null;
    }

    public function validate($number, $countryCode)
    {
        try {
            $numberProto = $this->phoneUtil->parse($number, $countryCode);
            return $this->phoneUtil->isValidNumber($numberProto);
        } catch (\Exception $e) {
            return false;
        }
    }
}