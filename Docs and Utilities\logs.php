<?php
// logs.php

// Path to Laravel log file
$logFile = '/home/<USER>/public_html/core/storage/logs/laravel.log';

// Get the log file content
$logContent = file_get_contents($logFile);

// Extract individual errors using regex
preg_match_all('/\[(.*?)\]\sproduction\.ERROR:(.*?)\{.*?\}/s', $logContent, $matches, PREG_SET_ORDER);

// Get the last 10 errors
$errors = array_slice(array_reverse($matches), 0, 5);

// Current timestamp
$timestamp = date('H:i:s d-m-Y');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Log Viewer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7faff;
            padding: 20px;
        }

        .log-container {
            max-width: 800px;
            margin: auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .log-entry {
            padding: 10px;
            background-color: #fffbf2;
            border-radius: 8px;
            margin-bottom: 15px;
            position: relative;
            border-left: 4px solid #f39c12;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            color: #3498db;
        }

        .copy-btn:hover {
            color: #2980b9;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #e74c3c;
        }

        .close-btn:hover {
            color: #c0392b;
        }

        .timestamp {
            font-size: inherit;
            color: inherit;
            font-weight: inherit;
        }
    </style>
</head>
<body>

<div class="log-container" id="logContainer">
    <div class="close-btn" onclick="closeLog()"><i class="fas fa-times"></i></div>
    <h3>Recent Laravel Errors <span class="timestamp">(<?= $timestamp ?>)</span></h3>

    <?php foreach ($errors as $error): ?>
        <div class="log-entry">
            <div><?= htmlspecialchars($error[1]) ?> - <?= htmlspecialchars(trim($error[2])) ?></div>
            <div class="copy-btn" onclick="copyToClipboard(this)"><i class="fas fa-copy"></i></div>
        </div>
    <?php endforeach; ?>

</div>

<script>
    function copyToClipboard(element) {
        const errorText = element.parentElement.innerText;
        navigator.clipboard.writeText(errorText).then(() => {
            element.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                element.innerHTML = '<i class="fas fa-copy"></i>';
            }, 2000);
        });
    }

    function closeLog() {
        document.getElementById('logContainer').style.display = 'none';
    }
</script>

</body>
</html>