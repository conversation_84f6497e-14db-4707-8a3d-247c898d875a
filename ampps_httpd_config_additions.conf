# AMPPS Apache Configuration for PHP Support
# Add these sections to your AMPPS httpd.conf file

# ========================================
# PHP MODULE CONFIGURATION
# ========================================

# Load PHP module (adjust path based on your AMPPS PHP version)
# For PHP 8.2.x in AMPPS:
LoadModule php_module "C:/Ampps/php/libphp.so"

# Alternative if above doesn't work:
# LoadModule php8_module "C:/Ampps/php/php8apache2_4.dll"

# ========================================
# PHP MIME TYPES AND HANDLERS
# ========================================

# Add PHP MIME type
<IfModule mime_module>
    AddType application/x-httpd-php .php .phtml .php3 .php4 .php5
    AddType application/x-httpd-php-source .phps
</IfModule>

# Set PHP handler
<IfModule php_module>
    PHPIniDir "C:/Ampps/php"
</IfModule>

# ========================================
# DIRECTORY INDEX CONFIGURATION
# ========================================

# Make sure index.php is recognized as a directory index
<IfModule dir_module>
    DirectoryIndex index.php index.html index.htm
</IfModule>

# ========================================
# DOCUMENT ROOT CONFIGURATION
# ========================================

# Your document root should point to AMPPS www directory
DocumentRoot "C:/Ampps/www"

# Directory permissions for your web root
<Directory "C:/Ampps/www">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
    
    # Enable .htaccess files
    AllowOverride All
    
    # PHP file handling
    <FilesMatch "\.php$">
        SetHandler application/x-httpd-php
    </FilesMatch>
</Directory>

# ========================================
# ADDITIONAL PHP CONFIGURATION
# ========================================

# Ensure PHP files are processed, not downloaded
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
</FilesMatch>

# Security: Hide .php source files
<FilesMatch "\.phps$">
    SetHandler application/x-httpd-php-source
</FilesMatch>

# ========================================
# REWRITE MODULE (for Laravel)
# ========================================

# Enable mod_rewrite for Laravel routing
LoadModule rewrite_module modules/mod_rewrite.so

# ========================================
# VIRTUAL HOST EXAMPLE (Optional)
# ========================================

# Uncomment and modify if you want a specific virtual host for FocLabs
#<VirtualHost *:80>
#    DocumentRoot "C:/Ampps/www/foclabs"
#    ServerName foclabs.local
#    
#    <Directory "C:/Ampps/www/foclabs">
#        Options Indexes FollowSymLinks
#        AllowOverride All
#        Require all granted
#        
#        <FilesMatch "\.php$">
#            SetHandler application/x-httpd-php
#        </FilesMatch>
#    </Directory>
#    
#    ErrorLog "logs/foclabs_error.log"
#    CustomLog "logs/foclabs_access.log" common
#</VirtualHost>
