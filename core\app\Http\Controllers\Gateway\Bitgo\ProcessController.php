<?php

namespace App\Http\Controllers\Gateway\Bitgo;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\PaymentController;
use App\Models\Deposit;
use App\Models\AdminNotification;
use App\Constants\Status;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ProcessController extends Controller
{
    /**
     * Process BitGo deposit
     *
     * @param Deposit $deposit
     * @return string JSON response
     */
    public static function process($deposit)
    {
        $gatewayCurrency = $deposit->gatewayCurrency();
        $bitgoAcc = json_decode($gatewayCurrency->gateway_parameter);

        // Check if we already have transaction details with an address
        if (!empty($deposit->detail)) {
            $existingDetails = json_decode($deposit->detail, true);
            if (isset($existingDetails['address']) && !empty($existingDetails['address'])) {
                // We already have an address, just return it
                Log::info('Using existing BitGo address', [
                    'transaction_id' => $deposit->trx,
                    'address' => $existingDetails['address']
                ]);

                // Create a data object for the view with the correct structure
                // This part might need to be re-evaluated if the amount calculation changes how existing details are handled.
                // For now, assume if address exists, we redirect.
                // However, the amounts might need to be re-sent to the view if they were wrong before.
                // For simplicity of this fix, if an address exists, we assume the previous process was sufficient or will be re-evaluated by user.
                // A more robust solution might re-calculate and re-save details if logic changes.
                $send['redirect'] = true;
                $send['redirect_url'] = route('user.deposit.confirm');
                return json_encode($send);
            }
        }

        // Set environment and base URL
        $environment = $bitgoAcc->environment ?? 'test';
        $baseUrl = $environment === 'prod'
            ? 'https://app.bitgo.com/api/v2'
            : 'https://app.test.bitgo.com/api/v2';

        // Determine the actual crypto coin for BitGo.
        // Assuming 'btc' for this BitGo wallet. This should ideally be configurable
        // if the same BitGo account/wallet ID can handle multiple coin types,
        // or if different BitGo gateway entries in the system map to different coins.
        // For this fix, we'll assume 'btc' as per the context of the problem.
        $actualBitgoCoin = 'btc'; // TODO: Make this configurable from $bitgoAcc->processing_coin or similar if needed.
        $coinForApiCall = $actualBitgoCoin;

        Log::info('BitGo Process: Determined coin for API call.', [
            'trx' => $deposit->trx,
            'coinForApiCall' => $coinForApiCall,
            'deposit_method_currency' => $deposit->method_currency
        ]);

        // Get current exchange rate and create address in a single process
        $result = self::createTransactionWithRate(
            $baseUrl,
            $bitgoAcc->api_key,
            $bitgoAcc->wallet_id,
            $coinForApiCall, // Use the corrected coin for API calls
            $deposit->method_currency, // This is still the user's input currency (e.g., USD)
            $deposit->trx
        );

        $rate = 0;
        // If we got a rate from the API, use it.
        if (!empty($result['rate'])) {
            $rate = $result['rate'];
            // Optionally, update the stored rate for the *actual crypto coin's* GatewayCurrency entry if desired,
            // but $gatewayCurrency here is for the user-selected one (e.g., 'USD' entry for BitGo).
            // For now, we just use the fetched rate.
            // Example: $btcGatewayCurrency = $deposit->gateway->currencies()->where('currency', strtoupper($coinForApiCall))->first();
            // if ($btcGatewayCurrency) { $btcGatewayCurrency->rate = $rate; $btcGatewayCurrency->save(); }
        } else {
            // If we couldn't get the rate from BitGo API, try to use a configured rate
            // for the *actual crypto coin* (e.g., BTC's GatewayCurrency entry).
            $cryptoGateway = $deposit->gateway->currencies()->where('currency', strtoupper($coinForApiCall))->first();
            if ($cryptoGateway && $cryptoGateway->rate > 0) {
                $rate = $cryptoGateway->rate;
                Log::info('BitGo Process: Using configured rate for actual crypto coin.', ['trx' => $deposit->trx, 'rate' => $rate, 'coin' => $coinForApiCall]);
            } else {
                 // Fallback to the rate from the originally selected $gatewayCurrency (e.g. USD entry for BitGo)
                 // This was Peter's definition: $gatewayCurrency->rate (for USD entry) holds BTC/USD price.
                $rate = $gatewayCurrency->rate;
                Log::warning('BitGo Process: Using rate from originally selected gateway currency as fallback.', ['trx' => $deposit->trx, 'rate' => $rate, 'original_gateway_currency' => $gatewayCurrency->currency]);
            }
        }

        if ($rate <= 0) {
            Log::error('BitGo Process: Invalid or zero rate for calculation.', [
                'trx' => $deposit->trx, 'rate' => $rate, 'coinForApiCall' => $coinForApiCall,
                'deposit_method_currency' => $deposit->method_currency
            ]);
            $send['error'] = true;
            $send['message'] = 'Exchange rate error. Unable to process payment.';
            return json_encode($send);
        }

        // Additional validation: Check if rate is reasonable for BTC (should be > 1000 USD typically)
        if ($coinForApiCall === 'btc' && $rate < 1000) {
            Log::warning('BitGo Process: BTC rate seems unusually low, might be inverted.', [
                'trx' => $deposit->trx, 'rate' => $rate, 'coinForApiCall' => $coinForApiCall
            ]);
        }

        // Calculate the correct crypto amount and fiat display amount
        $calculatedCryptoAmount = 0;
        $displayedFiatAmount = 0;

        $logContext = [
            'trx' => $deposit->trx,
            'deposit_amount_input' => $deposit->amount, // Original amount entered by user
            'deposit_method_currency_input' => $deposit->method_currency, // Original currency selected by user
            'deposit_final_amount_stored' => $deposit->final_amount, // Value from PaymentController
            'rate_for_calculation' => $rate, // BTC/USD rate
            'coin_for_api_call' => $coinForApiCall
        ];

        // CRITICAL DEBUG: Log what we're receiving from the deposit record
        Log::error('BitGo ProcessController DEBUG - Deposit Values Received:', [
            'trx' => $deposit->trx,
            'deposit->amount' => $deposit->amount,
            'deposit->final_amount' => $deposit->final_amount,
            'deposit->method_currency' => $deposit->method_currency,
            'expected_amount_should_be' => 200,
            'problem_if_amount_is_21_million' => ($deposit->amount > 1000000) ? 'YES - PROBLEM DETECTED' : 'NO - LOOKS OK'
        ]);

        if (strtoupper($deposit->method_currency) == 'USD') {
            // User specified deposit in USD, $deposit->amount is the USD value
            $displayedFiatAmount = $deposit->amount; // The original USD amount
            $calculatedCryptoAmount = $displayedFiatAmount / $rate; // Convert USD to crypto
            
            // CRITICAL DEBUG: Log the calculation
            Log::error('BitGo ProcessController DEBUG - Calculation:', [
                'trx' => $deposit->trx,
                'displayedFiatAmount_source' => 'deposit->amount',
                'displayedFiatAmount_value' => $displayedFiatAmount,
                'rate' => $rate,
                'calculatedCryptoAmount' => $calculatedCryptoAmount,
                'should_be_around_0.00183_BTC' => ($calculatedCryptoAmount < 0.01) ? 'YES - CORRECT' : 'NO - WRONG'
            ]);
            
            Log::info('BitGo Calculation: USD input to Crypto.', array_merge($logContext, [
                'calculated_crypto' => $calculatedCryptoAmount, 
                'displayed_fiat' => $displayedFiatAmount,
                'calculation_detail' => "$displayedFiatAmount USD / $rate rate = $calculatedCryptoAmount " . strtoupper($coinForApiCall)
            ]));
        } elseif (strtoupper($deposit->method_currency) == strtoupper($coinForApiCall)) {
            // User specified deposit in the crypto itself (e.g., BTC for BTC wallet)
            // $deposit->amount is the crypto amount.
            // $deposit->final_amount is its USD equivalent calculated at depositInsert.
            $displayedFiatAmount = $deposit->amount; // Use original USD amount entered by user
            $calculatedCryptoAmount = $displayedFiatAmount / $rate; // Convert USD to BTC
            // For more accuracy with current rate for display: $displayedFiatAmount = $calculatedCryptoAmount * $rate;
            Log::info('BitGo Calculation: Crypto input.', array_merge($logContext, ['calculated_crypto' => $calculatedCryptoAmount, 'displayed_fiat' => $displayedFiatAmount]));
        } else {
            Log::error('BitGo Error: Mismatch or unsupported method_currency for calculation.', $logContext);
            $send['error'] = true;
            $send['message'] = 'Unsupported currency for this payment method. Please contact support.';
            return json_encode($send);
        }

        // If address creation failed (e.g. API error from createTransactionWithRate), handle it
        // The $result['success'] check is important here.
        // The original code had a section for fetching existing addresses if creation failed. This should be preserved.
        // The $coinForApiCall should be used in that fallback logic too.

        if (!$result['success']) {
            Log::warning('BitGo address creation/fetch failed initially, trying fallback if applicable.', [
                'transaction_id' => $deposit->trx,
                'error' => $result['message'] ?? 'Unknown error from createTransactionWithRate'
            ]);
            // Fallback logic for fetching existing addresses (adapted from original code)
            try {
                $existingAddressesResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $bitgoAcc->api_key,
                    'Content-Type' => 'application/json',
                ])->get($baseUrl . '/' . $coinForApiCall . '/wallet/' . $bitgoAcc->wallet_id . '/addresses', [ // Use $coinForApiCall
                    'limit' => 50,
                    'mine' => true
                ]);

                if ($existingAddressesResponse->successful()) {
                    $addresses = $existingAddressesResponse->json()['addresses'] ?? [];
                    $foundAddress = false;
                    foreach ($addresses as $addr) {
                        if (isset($addr['label']) && $addr['label'] === 'Payment ' . $deposit->trx) {
                            $result['address'] = $addr['address'];
                            $result['success'] = true; // Mark as success if found
                            $foundAddress = true;
                            Log::info('BitGo Fallback: Found existing labeled address.', ['trx' => $deposit->trx, 'address' => $addr['address']]);
                            break;
                        }
                    }
                    if (!$foundAddress && !empty($addresses)) {
                        // If no labeled address, consider using an existing one (be cautious with reuse)
                        // For now, let's assume if createTransactionWithRate failed and no labeled one, it's an error.
                        // Original code used addresses[0]['address'], this might not be safe.
                        // Let's ensure $result['success'] remains false if no specific address is confirmed.
                        Log::warning('BitGo Fallback: No labeled address found, and initial creation failed.', ['trx' => $deposit->trx]);
                    }
                } else {
                    Log::error('BitGo Fallback: Error fetching existing addresses.', ['trx' => $deposit->trx, 'status' => $existingAddressesResponse->status()]);
                }
            } catch (\Exception $e) {
                Log::error('BitGo Fallback: Exception fetching existing addresses.', ['trx' => $deposit->trx, 'message' => $e->getMessage()]);
            }
        }
        
        // Final check for success after all attempts to get an address
        if (!$result['success'] || empty($result['address'])) {
            $send['error'] = true;
            $send['message'] = $result['message'] ?? 'Failed to obtain a deposit address.';
            return json_encode($send);
        }

        // Update the $result array with the correctly calculated crypto amount and actual crypto currency
        $result['amount'] = $calculatedCryptoAmount;
        $result['currency'] = strtoupper($coinForApiCall); // Ensure this is the actual crypto coin
        // $result['rate'] is already set from createTransactionWithRate or fallback

        $deposit->detail = json_encode($result); // Save all details including address, correct crypto amount, rate, etc.
        $deposit->save();

        // Log what we're sending to the view
        Log::info('BitGo payment data for view (final)', [
            'trx' => $deposit->trx,
            'view_address' => $result['address'],
            'view_crypto_amount' => $calculatedCryptoAmount,
            'view_crypto_currency' => strtoupper($coinForApiCall),
            'view_fiat_amount' => $displayedFiatAmount,
            'view_fiat_currency_input' => $deposit->method_currency,
            'rate_used_for_final_calc' => $rate
        ]);

        // Create a data object for the view with the correct structure
        $send['val'] = [
            'address' => $result['address'],
            'amount' => $calculatedCryptoAmount,
            'currency' => strtoupper($coinForApiCall), // e.g., 'BTC'
            'transaction_id' => $deposit->trx,
            'fiat_amount' => $displayedFiatAmount,
            'fiat_currency' => (strtoupper($deposit->method_currency) == 'USD' ? 'USD' : ($deposit->method_fiat ?? 'USD')),
            'exchange_rate' => $rate // The BTC/USD exchange rate used for calculation
        ];
        $send['view'] = 'user.payment.Bitgo';
        $send['method'] = 'GET';
        $send['url'] = route('user.deposit.confirm');

        return json_encode($send);
    }

    /**
     * Create a new transaction (get deposit address) and get exchange rate in one process
     *
     * @param string $baseUrl BitGo API base URL
     * @param string $apiKey BitGo API key
     * @param string $walletId BitGo wallet ID
     * @param string $coin Cryptocurrency code (lowercase)
     * @param string $currency Currency code
     * @param string $externalId External transaction ID
     * @return array Response with address, rate and other details
     */
    private static function createTransactionWithRate($baseUrl, $apiKey, $walletId, $coin, $currency, $externalId)
    {
        try {
            // Get server IP address
            $serverIp = self::getServerIp();

            // First, get the exchange rate
            $rate = null;
            try {
                // Get market data from BitGo API
                $marketResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ])->get($baseUrl . '/market/latest');

                if ($marketResponse->successful()) {
                    $marketData = $marketResponse->json();

                    // Find the exchange rate for the specified crypto/fiat pair
                    if (isset($marketData['marketData'])) {
                        foreach ($marketData['marketData'] as $data) {
                            if (strtolower($data['coin']) === $coin &&
                                isset($data['currencies']) &&
                                isset($data['currencies']['USD'])) {

                                $rate = $data['currencies']['USD']['last'];

                                Log::info("BitGo exchange rate retrieved", [
                                    'crypto' => strtoupper($coin),
                                    'rate' => $rate
                                ]);

                                break;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Just log the error but continue with address creation
                Log::warning('Error getting exchange rate from BitGo', [
                    'message' => $e->getMessage()
                ]);
            }

            // Log the request details
            Log::info('BitGo creating address', [
                'wallet_id' => $walletId,
                'coin' => $coin,
                'external_id' => $externalId,
                'url' => $baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address',
                'server_ip' => $serverIp
            ]);

            // Create a new address for receiving payment
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->post($baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address', [
                'label' => 'Payment ' . $externalId,
            ]);

            // Log the full response for debugging
            Log::debug('BitGo address creation response', [
                'status' => $response->status(),
                'body' => $response->body(),
                'headers' => $response->headers(),
                'server_ip' => $serverIp
            ]);

            if (!$response->successful()) {
                // If creation failed because address already exists, try to get existing addresses
                if ($response->status() === 400 && strpos($response->body(), 'already exists') !== false) {
                    Log::info('Address already exists, fetching existing addresses', [
                        'external_id' => $externalId,
                        'server_ip' => $serverIp
                    ]);

                    // Try to get existing addresses with this label
                    $existingResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $apiKey,
                        'Content-Type' => 'application/json',
                    ])->get($baseUrl . '/' . $coin . '/wallet/' . $walletId . '/addresses', [
                        'limit' => 50,
                        'mine' => true
                    ]);

                    if ($existingResponse->successful()) {
                        $addresses = array();
                        $responseData = $existingResponse->json();
                        if (isset($responseData['addresses'])) {
                            $addresses = $responseData['addresses'];
                        }

                        // Try to find an address with our label
                        foreach ($addresses as $addr) {
                            if (isset($addr['label']) && $addr['label'] === 'Payment ' . $externalId) {
                                return [
                                    'success' => true,
                                    'address' => $addr['address'],
                                    'currency' => $currency,
                                    'transaction_id' => $externalId,
                                    'wallet_id' => $walletId,
                                    'rate' => $rate
                                ];
                            }
                        }
                    }
                }

                // Check for IP restriction error
                $errorMessage = $response->body();
                $isIpRestricted = strpos($errorMessage, 'IP-restricted token') !== false;

                Log::error('BitGo address creation failed', [
                    'response_body' => $errorMessage,
                    'status' => $response->status(),
                    'wallet_id' => $walletId,
                    'coin' => $coin,
                    'url' => $baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address',
                    'server_ip' => $serverIp,
                    'is_ip_restricted' => $isIpRestricted
                ]);

                // Create admin notification for IP restriction error
                if ($isIpRestricted) {
                    $adminNotification = new AdminNotification();
                    $adminNotification->user_id = 0;
                    $adminNotification->title = 'BitGo IP Restriction Error';
                    $adminNotification->click_url = '#';
                    $adminNotification->message = "BitGo API key has IP restrictions. Server IP: {$serverIp} is not allowed. Please add this IP to the allowed list in BitGo settings.";
                    $adminNotification->save();

                    return [
                        'success' => false,
                        'message' => "BitGo API key has IP restrictions. Server IP: {$serverIp} is not allowed. Please add this IP to the allowed list in BitGo settings.",
                        'rate' => $rate
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Failed to create address: ' . ($errorMessage),
                    'rate' => $rate
                ];
            }

            $addressData = $response->json();

            return [
                'success' => true,
                'address' => $addressData['address'],
                'currency' => $currency,
                'transaction_id' => $externalId,
                'wallet_id' => $walletId,
                'rate' => $rate
            ];

        } catch (\Exception $e) {
            $serverIp = self::getServerIp();

            Log::error('BitGo API exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'server_ip' => $serverIp
            ]);

            return [
                'success' => false,
                'message' => 'API Error: ' . $e->getMessage(),
                'rate' => null
            ];
        }
    }

    /**
     * Get server IP address
     *
     * @return string Server IP address
     */
    private static function getServerIp()
    {
        try {
            // Try to get server IP from external service
            $response = Http::get('https://api.ipify.org');
            if ($response->successful()) {
                return $response->body();
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get server IP from external service', [
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to server variables
        $serverVars = ['SERVER_ADDR', 'LOCAL_ADDR'];
        foreach ($serverVars as $var) {
            if (isset($_SERVER[$var])) {
                return $_SERVER[$var];
            }
        }

        return '127.0.0.1'; // Default fallback
    }

    /**
     * Check BitGo transaction status
     * Called by AJAX from the payment page
     *
     * @param Request $request
     * @param string $trx Transaction ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkTransaction(Request $request, $trx)
    {
        Log::info('BitGo CheckTransaction: Received request.', ['trx' => $trx]);

        // Find the deposit with the given transaction ID
        $deposit = Deposit::where('trx', $trx)->orderBy('id', 'DESC')->first();

        if (!$deposit) {
            Log::warning('BitGo CheckTransaction: Deposit not found.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Transaction not found.'
            ]);
        }

        // If already processed, return success
        if ($deposit->status == Status::PAYMENT_SUCCESS) {
            return response()->json([
                'status' => true,
                'status_code' => 'completed',
                'message' => 'Payment already confirmed.',
                'confirmations' => 3
            ]);
        }

        // Only check status for initiated payments
        if ($deposit->status != Status::PAYMENT_INITIATE) {
            Log::warning('BitGo CheckTransaction: Deposit not in initiated status.', [
                'trx' => $trx,
                'status' => $deposit->status
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Transaction is not in a valid state for checking.'
            ]);
        }

        $gatewayCurrency = $deposit->gatewayCurrency();
        if (!$gatewayCurrency) {
            Log::error('BitGo CheckTransaction: Gateway currency not found.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Payment method configuration error.'
            ]);
        }

        $bitgoAcc = json_decode($gatewayCurrency->gateway_parameter);
        if (!$bitgoAcc || !isset($bitgoAcc->api_key) || !isset($bitgoAcc->wallet_id)) {
            Log::error('BitGo CheckTransaction: Invalid gateway configuration.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Payment gateway configuration error.'
            ]);
        }

        $depositDetails = json_decode($deposit->detail, true);
        if (!$depositDetails || !isset($depositDetails['address'])) {
            Log::error('BitGo CheckTransaction: Invalid deposit details.', [
                'trx' => $trx,
                'details' => $deposit->detail
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Payment details incomplete. Please contact support.'
            ]);
        }

        // Extract necessary data from deposit details
        $coin = strtolower($deposit->method_currency);
        $walletId = $depositDetails['wallet_id'] ?? $bitgoAcc->wallet_id;

        // Define minimum confirmations per cryptocurrency
        $minConfirmations = [
            'btc' => 3,    // Bitcoin
            'tbtc' => 2,   // Bitcoin Testnet
            'eth' => 12,   // Ethereum
            'ltc' => 6,    // Litecoin
            'bch' => 3,    // Bitcoin Cash
            'dash' => 6,   // Dash
            'doge' => 6,   // Dogecoin
            'xrp' => 1,    // Ripple
            'xlm' => 1,    // Stellar
            'zec' => 12,   // Zcash
            'bsv' => 6,    // Bitcoin SV
            'etc' => 400,  // Ethereum Classic
            'trx' => 20,   // TRON
            'algo' => 1,   // Algorand
            'eos' => 1,    // EOS
            'hbar' => 1,   // Hedera
            'xtz' => 30,   // Tezos
            'ada' => 15,   // Cardano
            'dot' => 6,    // Polkadot
            'sol' => 32,   // Solana
            'avax' => 1,   // Avalanche
            'matic' => 128 // Polygon
        ];

        // Get required confirmations for this coin, default to 3 if not specified
        $requiredConfirmations = $minConfirmations[$coin] ?? 3;

        // Override with gateway settings if available
        if (isset($bitgoAcc->min_confirmations)) {
            $requiredConfirmations = (int)$bitgoAcc->min_confirmations;
        }

        try {
            // Use the BitgoAPI Support class to check transaction status
            $environment = $bitgoAcc->environment ?? 'test';

            // Import the BitgoAPI class
            $bitgoApiClass = '\App\Http\Controllers\Gateway\Bitgo\Support\BitgoAPI';
            if (!class_exists($bitgoApiClass)) {
                Log::error('BitGo CheckTransaction: BitgoAPI class not found.', ['trx' => $trx]);
                return response()->json([
                    'status' => false,
                    'message' => 'System configuration error.'
                ]);
            }

            $bitgoApi = new $bitgoApiClass(
                $bitgoAcc->api_key,
                $walletId,
                $environment
            );

            // Get transaction status using the existing method
            $status = $bitgoApi->getTransactionStatus(json_encode($depositDetails));

            Log::info('BitGo CheckTransaction: Status check result', [
                'trx' => $trx,
                'status' => $status,
                'coin' => $coin,
                'required_confirmations' => $requiredConfirmations
            ]);

            // Map BitgoAPI status to frontend expected status
            switch ($status) {
                case 'completed':
                    // Update deposit status in database
                    PaymentController::userDataUpdate($deposit);
                    return response()->json([
                        'status' => true,
                        'status_code' => 'completed',
                        'message' => 'Payment confirmed.',
                        'confirmations' => $requiredConfirmations
                    ]);

                case 'pending':
                    // For pending transactions, we've detected the payment but it needs more confirmations
                    return response()->json([
                        'status' => true,
                        'status_code' => 'pending',
                        'message' => 'Payment detected, awaiting confirmations.',
                        'confirmations' => 1
                    ]);

                case 'waiting':
                    // Check if payment window has expired
                    $paymentWindowMinutes = $bitgoAcc->payment_window_minutes ?? 30; // Default 30 minutes
                    if ($deposit->created_at->addMinutes($paymentWindowMinutes)->isPast()) {
                        return response()->json([
                            'status' => true,
                            'status_code' => 'expired',
                            'message' => 'Payment window has expired.'
                        ]);
                    }

                    return response()->json([
                        'status' => true,
                        'status_code' => 'waiting',
                        'message' => 'Awaiting payment.',
                        'confirmations' => 0
                    ]);

                case 'error':
                case 'unknown':
                default:
                    return response()->json([
                        'status' => false,
                        'message' => 'Error checking payment status. Please try again.'
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('BitGo CheckTransaction: Exception occurred.', [
                'trx' => $trx,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => 'An error occurred while checking payment status: ' . $e->getMessage()
            ]);
        }
    }
}
