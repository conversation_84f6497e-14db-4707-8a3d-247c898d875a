<?php
/**
 * Simple Crypto Payment UI Test
 *
 * This file demonstrates the modern UI for crypto payments
 * with QR code integration.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to generate QR code using external API
function generateQrCode($data, $size = 240) {
    // Log the QR code generation method
    error_log("Using external API for QR code generation");
    
    // Use a simple QR code image from an external API
    return '<div style="text-align: center;">
        <img src="https://api.qrserver.com/v1/create-qr-code/?data=' . urlencode($data) . '&size=' . $size . 'x' . $size . '" alt="QR Code" style="max-width: ' . $size . 'px;">
        <!-- Using external API for QR code generation -->
    </div>';
}

// Test data
$amount = '0.001';
$currency = 'BTC';
$address = '**********************************'; // Test Bitcoin address
$order_id = 'TEST' . time();
$usd_amount = '50.00';
$expiry_minutes = 30;

// Generate QR code
$qrCode = generateQrCode("bitcoin:{$address}?amount={$amount}");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Crypto Payment Test Page">
    <title>Bitcoin Payment</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS for Payment Box -->
    <style>
        html {
            font-size: 16.8px; /* 14px increased by 20% */
        }
        @media (min-width: 768px) {
            html {
                font-size: 19.2px; /* 16px increased by 20% */
            }
            .tooltip-inner {
                max-width: 350px;
            }
        }
        .payment-container {
            max-width: 980px;
            margin: 0 auto;
        }
        .box-shadow {
            box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
        }
        .payment-box {
            border: 1px solid #d1d9e6;
            border-radius: 1rem;
            padding: 1.8rem; /* 1.5rem increased by 20% */
            margin-bottom: 2.4rem; /* 2rem increased by 20% */
            background-color: #fff;
            box-shadow:
                0.5rem 0.5rem 1rem rgba(0, 0, 0, 0.1),
                -0.5rem -0.5rem 1rem rgba(255, 255, 255, 0.8),
                inset 0 0 0 rgba(255, 255, 255, 0.8),
                inset 0 0 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container payment-container pt-3 pb-5">
        <div class="row">
            <div class="col-md-12">
                <div class="payment-box">
                    <div class="payment-header">
                        <h2 class="payment-title">Bitcoin Payment</h2>
                        <p class="text-muted">Order #<?php echo $order_id; ?></p>
                    </div>

                    <div class="payment-amount">
                        <div class="d-flex justify-content-between align-items-center">
                            <div style="font-size: 2.4rem; font-weight: 700;">
                                <span id="crypto-amount"><?php echo $amount; ?></span> <span id="crypto-currency"><?php echo $currency; ?></span>
                            </div>
                            <div style="font-size: 2.4rem; font-weight: 700;">
                                ≈ $<?php echo $usd_amount; ?> USD
                            </div>
                        </div>
                    </div>

                    <div class="payment-instructions" style="background-color: #e9f7fe; color: #0c5460; padding: 1.5rem; border-radius: 0.8rem; margin-bottom: 1.5rem; position: relative; overflow: hidden; border: 1px solid rgba(12, 84, 96, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(12, 84, 96, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                        <!-- Gradient overlay for 3D effect -->
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                        <p style="font-size: 1.5rem; margin-bottom: 1rem; position: relative;">Send exactly <strong style="font-size: 1.7rem;"><span id="crypto-amount-repeat"><?php echo $amount; ?></span> <span id="crypto-currency-repeat"><?php echo $currency; ?></span></strong> to this address:</p>
                    </div>

                    <div class="payment-address" style="font-family: monospace; font-size: 1.32rem; word-break: break-all; margin-bottom: 1rem; padding: 0.8rem; background-color: #f8f9fa; border-radius: 0.5rem; box-shadow: inset 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.05), inset -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.7); border: 1px solid rgba(0, 0, 0, 0.05);">
                        <div class="d-flex justify-content-between align-items-center">
                            <span id="crypto-address"><?php echo $address; ?></span>
                            <button class="btn btn-outline-secondary btn-copy" data-clipboard-target="#crypto-address" title="Copy Address" style="padding: 0.5rem 1rem;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- New layout with QR code on left and status on right -->
                    <div class="row">
                        <div class="col-md-5">
                            <!-- QR Code -->
                            <div id="qrcode" style="text-align: center; margin-bottom: 1.5rem;">
                                <?php echo $qrCode; ?>
                            </div>
                            <p style="font-size: 1.2rem; color: #0c5460; font-weight: 500; text-align: center;">Scan with your wallet app</p>
                        </div>
                        <div class="col-md-7">
                            <!-- Payment status -->
                            <div class="payment-status status-waiting" style="padding: 1.2rem; border-radius: 0.8rem; margin-bottom: 1.5rem; background-color: #fff3cd; color: #856404; box-shadow: 0.2rem 0.2rem 0.5rem rgba(133, 100, 4, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5); position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.05);">
                                <!-- Gradient overlay for 3D effect -->
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                                <div class="d-flex align-items-center" style="position: relative;">
                                    <i class="fas fa-clock me-3" style="font-size: 1.8rem;"></i>
                                    <div>
                                        <strong style="font-size: 1.44rem; display: block; margin-bottom: 0.5rem;">Waiting for payment</strong>
                                        <div style="font-size: 1.2rem;" id="status-message">Please send the exact amount to the address shown above</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Timer -->
                            <div class="text-center">
                                <div style="font-size: 1.2rem; color: #856404; font-weight: 500;">Payment expires in</div>
                                <div class="timer" id="payment-timer" style="font-size: 2.2rem;"><?php echo $expiry_minutes; ?>:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-3 mt-4">
                        <button class="btn btn-outline-secondary btn-lg py-3" id="btn-check-payment" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-sync-alt me-2"></i> Check Payment Status</span>
                        </button>
                        <a href="https://www.blockchain.com/explorer/addresses/btc/<?php echo $address; ?>" target="_blank" class="btn btn-primary btn-lg py-3" id="btn-open-explorer" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-search me-2"></i> Open in Blockchain Explorer</span>
                        </a>
                    </div>

                    <div class="payment-footer" style="border-top: 1px solid #e9ecef; padding-top: 1rem; margin-top: 1.5rem; font-size: 1.08rem; color: #6c757d;">
                        <div class="row">
                            <div class="col-md-6">
                                <small>Transaction ID: <span id="transaction-id">N/A</span></small>
                            </div>
                            <div class="col-md-6 text-end">
                                <small>Powered by Your Company</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Received Box (initially hidden) -->
                <div class="payment-box d-none" id="payment-received-box">
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                        <h2 class="mt-4" style="font-size: 2.4rem;">Payment Received!</h2>
                        <p class="lead" style="font-size: 1.44rem;">Thank you for your payment.</p>
                        <p style="font-size: 1.2rem;">Transaction ID: <span id="success-transaction-id">N/A</span></p>
                        <div class="mt-5">
                            <a href="#" class="btn btn-success btn-lg py-3 px-5" style="font-size: 1.2rem;">Continue to Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>

    <!-- Custom JavaScript for Payment Box -->
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            try {
                $('[data-bs-toggle="tooltip"]').tooltip();
            } catch (e) {
                console.warn('Bootstrap tooltip initialization failed:', e);
            }

            // Initialize clipboard.js
            try {
                var clipboard = new ClipboardJS('.btn-copy');

                clipboard.on('success', function(e) {
                    // Show a simple alert if tooltip fails
                    try {
                        $(e.trigger).tooltip({
                            title: 'Copied!',
                            placement: 'top',
                            trigger: 'manual'
                        }).tooltip('show');

                        setTimeout(function() {
                            $(e.trigger).tooltip('hide');
                        }, 1000);
                    } catch (tooltipError) {
                        alert('Address copied to clipboard!');
                    }

                    e.clearSelection();
                });

                clipboard.on('error', function(e) {
                    console.error('Clipboard copy failed:', e);
                    alert('Failed to copy. Please select and copy the address manually.');
                });
            } catch (e) {
                console.error('Clipboard.js initialization failed:', e);
                // Fallback for clipboard functionality
                $('.btn-copy').on('click', function() {
                    var text = $(this).data('clipboard-text');
                    prompt('Copy this address manually:', text);
                });
            }

            // Check payment status button
            $('#btn-check-payment').click(function() {
                var $btn = $(this);
                $btn.prop('disabled', true);
                $btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Checking...');

                // Simulate checking payment status
                setTimeout(function() {
                    $btn.prop('disabled', false);
                    $btn.html('<i class="fas fa-sync-alt me-2"></i> Check Payment Status');

                    // For demo purposes, randomly show payment received
                    if (Math.random() > 0.7) {
                        showPaymentReceived();
                    }
                }, 2000);
            });

            // Payment timer
            var timeLeft = <?php echo $expiry_minutes; ?> * 60; // Convert minutes to seconds
            var timerInterval = setInterval(function() {
                timeLeft--;

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    showPaymentExpired();
                } else {
                    var minutes = Math.floor(timeLeft / 60);
                    var seconds = timeLeft % 60;
                    $('#payment-timer').text(
                        (minutes < 10 ? '0' : '') + minutes + ':' +
                        (seconds < 10 ? '0' : '') + seconds
                    );
                }
            }, 1000);

            // Function to show payment received
            function showPaymentReceived() {
                $('.payment-box').first().addClass('d-none');
                $('#payment-received-box').removeClass('d-none');
                $('#success-transaction-id').text('txid_' + Math.random().toString(36).substr(2, 9));
                clearInterval(timerInterval);
            }

            // Function to show payment expired
            function showPaymentExpired() {
                $('.payment-status')
                    .removeClass('status-waiting')
                    .addClass('status-expired')
                    .css('background-color', '#f8d7da')
                    .css('color', '#721c24')
                    .html('<div class="d-flex align-items-center"><i class="fas fa-exclamation-circle me-3" style="font-size: 1.8rem;"></i><div><strong style="font-size: 1.44rem; display: block; margin-bottom: 0.5rem;">Payment Expired</strong><div style="font-size: 1.2rem;">The payment time has expired. Please start over.</div></div></div>');

                $('#btn-check-payment, #btn-open-explorer').prop('disabled', true);
            }
        });
    </script>
</body>
</html>
