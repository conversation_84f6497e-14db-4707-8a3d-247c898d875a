@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="card-title">@lang('MoonPay Payment')</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group text-center">
                        <li class="list-group-item d-flex justify-content-between">
                            @lang('You have to pay '):
                            <strong>{{ showAmount($deposit->final_amount, currencyFormat: false) }} {{ __($deposit->method_currency) }}</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            @lang('You will get '):
                            <strong>{{ showAmount($deposit->amount) }}</strong>
                        </li>
                    </ul>
                    <button type="button" class="btn btn--base w-100 mt-3" id="moonpay-button">@lang('Pay with MoonPay')</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($){
        "use strict";
        
        const moonpayBtn = document.getElementById('moonpay-button');
        moonpayBtn.addEventListener('click', function() {
            try {
                const baseUrl = "{{ $data->environment }}" === 'sandbox' 
                    ? 'https://buy-sandbox.moonpay.com' 
                    : 'https://buy.moonpay.com';

                const params = new URLSearchParams({
                    apiKey: "{{ $data->apiKey }}",
                    currencyCode: "{{ strtolower($deposit->method_currency) }}", // The fiat currency
                    baseCurrencyAmount: "{{ $data->amount }}",
                    externalTransactionId: "{{ $deposit->trx }}",
                    redirectURL: "{{ route('ipn.'.$deposit->gateway->alias) }}"
                });

                const url = `${baseUrl}?${params.toString()}`;
                console.log('MoonPay URL:', url);

                window.open(url, '_blank');

            } catch (error) {
                console.error('Error launching MoonPay:', error);
                alert('Unable to launch payment window. Please try again.');
            }
        });
    })(jQuery);
</script>
@endpush




