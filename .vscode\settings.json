{
    // File Explorer - Hide files and folders that should be ignored
    "files.exclude": {
        // Laravel vendor directory (Composer dependencies)
        "**/core/vendor": true,
        
        // Laravel storage directories (cache, logs, sessions)
        "**/core/storage/logs": true,
        "**/core/storage/framework/cache": true,
        "**/core/storage/framework/sessions": true,
        "**/core/storage/framework/views": true,
        "**/core/storage/debugbar": true,
        "**/core/storage/app/public": true,
        
        // Laravel bootstrap cache
        "**/core/bootstrap/cache": true,
        
        // Laravel temp directory
        "**/core/temp": true,
        
        // Environment files (sensitive)
        "**/core/.env": true,
        "**/core/.env.*": true,
        
        // Node modules
        "**/core/node_modules": true,
        
        // Composer lock file
        "**/core/composer.lock": true,
        
        // Laravel specific files
        "**/core/.phpunit.result.cache": true,
        "**/core/Homestead.json": true,
        "**/core/Homestead.yaml": true,
        "**/core/npm-debug.log": true,
        "**/core/yarn-error.log": true,
        
        // OS generated files
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/.Spotlight-V100": true,
        "**/.Trashes": true,
        "**/ehthumbs.db": true,
        
        // IDE files
        "**/.idea": true,
        
        // Temporary files
        "**/*.tmp": true,
        "**/*.temp": true,
        "**/*.swp": true,
        "**/*.swo": true,
        "**/*~": true,
        
        // Backup files
        "**/*.bak": true,
        "**/*.backup": true,
        "*/*_bak.*": true,
        "*/*_backup.*": true
    },
    
    // Search - Exclude the same patterns from search
    "search.exclude": {
        "**/core/vendor": true,
        "**/core/storage": true,
        "**/core/bootstrap/cache": true,
        "**/core/node_modules": true,
        "**/core/.env": true,
        "**/core/.env.*": true,
        "**/.git": true,
        "**/*.log": true
    },
    
    // File Watcher - Exclude from file watching for better performance
    "files.watcherExclude": {
        "**/core/vendor/**": true,
        "**/core/storage/**": true,
        "**/core/bootstrap/cache/**": true,
        "**/core/node_modules/**": true,
        "**/core/temp/**": true,
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true
    },
    
    // Git - Respect .gitignore files
    "git.ignoreLimitWarning": true,
    "git.useEditorAsCommitInput": false,
    
    // PHP specific settings for Laravel
    "php.suggest.basic": false,
    "php.validate.enable": true,
    
    // Auto-save settings
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    
    // Editor settings for PHP/Laravel
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    
    // Language specific settings
    "[php]": {
        "editor.defaultFormatter": "bmewburn.vscode-intelephense-client",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    
    "[blade]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.autoClosingBrackets": "always"
    },
    
    "[json]": {
        "editor.tabSize": 2,
        "editor.insertSpaces": true
    },
    
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    
    // Extensions recommendations (optional)
    "extensions.recommendations": [
        "bmewburn.vscode-intelephense-client",
        "onecentlin.laravel-blade",
        "ryannaddy.laravel-artisan",
        "codingyu.laravel-goto-view",
        "amiralizadeh9480.laravel-extra-intellisense"
    ],
    "augment.conflictingCodingAssistantCheck": false,
    "augment.advanced": {
        
    }
}
