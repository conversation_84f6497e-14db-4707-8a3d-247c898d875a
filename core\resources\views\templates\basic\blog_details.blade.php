@extends($activeTemplate . 'layouts.frontend')
@section('content')
    <section class="pt-100 pb-100 custom--bg-two">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="blog-details-wrapper">
                        <div class="blog-details__thumb">
                            <img src="{{ frontendImage('blog', @$blog->data_values->image, '860x570') }}" alt="image">
                            <div class="post__date">
                                <span class="date">{{ showDateTime($blog->created_at, 'd') }}</span>
                                <span class="month">{{ showDateTime($blog->created_at, 'M') }}</span>
                            </div>
                        </div>
                        <div class="blog-details__content">
                            <h4 class="blog-details__title mb-3">{{ __(@$blog->data_values->title) }}</h4>
                            {!! @$blog->data_values->description_nic !!}
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="fb-comments" data-href="{{ url()->current() }}" data-numposts="5"></div>
                    </div>
                    <div class="mt-4">
                        <h5 class="blog-sidebar__title mt-0 mb-2">@lang('Share')</h5>
                        <ul class="list list--row flex-wrap social-list">
                            <li>
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"
                                    class="social-list__icon">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            </li>
                            <li>
                                <a href="https://twitter.com/intent/tweet?text={{ __(@$blog->data_values->title) }}%0A{{ url()->current() }}"
                                    class="social-list__icon">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            </li>
                            <li>
                                <a class="social-list__icon"
                                    href="https://pinterest.com/pin/create/bookmarklet/?media={{ frontendImage('blog', @$blog->data_values->blog_image, '800x580') }}&url={{ urlencode(url()->current()) }}">
                                    <i class="fab fa-pinterest-p"></i>
                                </a>
                            </li>
                            <li>
                                <a class="social-list__icon"
                                    href="http://www.linkedin.com/shareArticle?mini=true&amp;url={{ urlencode(url()->current()) }}&amp;title={{ __(@$blog->data_values->title) }}&amp;summary={{ __(@$blog->data_values->short_details) }}">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 pl-lg-5">
                    <div class="sidebar">
                        <div class="widget">
                            <h3 class="widget-title">@lang('Recent Blog Posts')</h3>
                            <ul class="small-post-list">
                                @foreach ($recentBlogs as $item)
                                    <li class="small-post-single">
                                        <div class="thumb">
                                            <img src="{{ frontendImage('blog', @$item->data_values->image, '430x280', thumb: true) }}"
                                                alt="image">
                                        </div>
                                        <div class="content">
                                            <h6 class="post-title">
                                                <a href="{{ route('blog.details', $item->slug) }}">
                                                    {{ __(strLimit(@$item->data_values->title, 80)) }}</a>
                                            </h6>
                                            <small>{{ showDateTime($item->created_at) }}</small>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('fbComment')
    @php echo loadExtension('fb-comment') @endphp
@endpush













