/* Copy Animation */
:root {
  --main: hsl(var(--base));

  --base-h: 49;
  --base-s: 92%;
  --base-l: 54%;
  --base: var(--base-h) var(--base-s) var(--base-l);

  --base-two-h: 213;
  --base-two-s: 100%;
  --base-two-l: 14%;
  --base-two: var(--base-two-h) var(--base-two-s) var(--base-two-l);

  --white-h: 0;
  --white-s: 0%;
  --white-l: 100%;
  --white: var(--white-h) var(--white-s) var(--white-l);
}

.copyInput {
  display: inline-block;
  line-height: 50px;
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  -webkit-transition: all .3s;
  -o-transition: all .3s;
  transition: all .3s;
}

.copied::after {
  position: absolute;
  top: 8px;
  right: 12%;
  width: 100px;
  display: block;
  content: "COPIED";
  font-size: 1em;
  padding: 5px 5px;
  color: #000;
  background-color: #37ebec;
  border-radius: 3px;
  opacity: 0;
  will-change: opacity, transform;
  animation: showcopied 1.5s ease;
}

@keyframes showcopied {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }

  50% {
    opacity: 0.7;
    transform: translateX(40%);
  }

  70% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
  }
}




.cookies-card {
  width: 520px;
  padding: 30px;
  color: #1E2337;
  position: fixed;
  bottom: 15px;
  left: 15px;
  z-index: 999999;
  transition: all .5s;
  background: #d1d1d1;
  border-radius: 5px;
}

.cookies-card.hide {
  bottom: -500px !important;
}

.radius--10px {
  border-radius: 10px;
}

.cookies-card__icon {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background-color: #6e6f70;
  color: #fff;
  font-size: 32px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.cookies-card__content {
  margin-bottom: 0;
}

.cookies-btn {
  color: #363636;
  text-decoration: none;
  padding: 10px 35px;
  margin: 3px 5px;
  display: inline-block;
  border-radius: 999px;
}

.cookies-btn:hover {
  color: #363636;
}


@media (max-width: 767px) {
  .cookies-card {
    width: 100%;
    left: 0;
    bottom: 0;
    font-size: 14px;
    padding: 15px;
  }
}




.hover-input-popup {
  position: relative;
}

.input-popup {
  display: none;
}

.hover-input-popup .input-popup {
  display: block;
  position: absolute;
  bottom: 100%;
  left: 50%;
  width: 280px;
  background-color: #1a1a1a;
  color: #fff;
  padding: 20px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.input-popup::after {
  position: absolute;
  content: '';
  bottom: -19px;
  left: 50%;
  margin-left: -5px;
  border-width: 10px 10px 10px 10px;
  border-style: solid;
  border-color: transparent transparent #1a1a1a transparent;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.input-popup p {
  padding-left: 20px;
  position: relative;
}

.input-popup p::before {
  position: absolute;
  content: '';
  font-family: 'Line Awesome Free';
  font-weight: 900;
  left: 0;
  top: 4px;
  line-height: 1;
  font-size: 18px;
}

.input-popup p.error {
  text-decoration: line-through;
}

.input-popup p.error::before {
  content: "\f057";
  color: #ea5455;
}

.input-popup p.success::before {
  content: "\f058";
  color: #28c76f;
}



.show-filter {
  display: none;
}

@media(max-width:767px) {
  .responsive-filter-card {
    display: none;
    transition: none;
  }

  .show-filter {
    display: block;
  }
}


.verification-code span {
  background-color: hsl(var(--base-two));
}

/* ////////////////// select 2 css //////////////// */
.select2-dropdown {
  border: 0 !important;
  margin-top: 8px !important;
  border-radius: 5px !important;
  box-shadow: 0 3px 9px rgba(50, 50, 9, 0.05), 6px 4px 19px rgb(115 103 240 / 20%)
}

.select2-search--dropdown {
  padding: 10px 10px !important;
  border-color: #ced4da !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: hsl(var(--base)) !important;
  padding: 10px 20px;
  background: transparent !important;
  color: hsl(var(--white)) !important;
  border-radius: 4px;
}

.select2-results__option.select2-results__option--selected,
.select2-results__option--selectable,
.select2-container--default .select2-results__option--disabled {
  padding: 12px 14px !important;
  border-bottom: 1px solid #eee;
}

.select2-results__option.select2-results__message {
  text-align: center !important;
  padding: 12px 14px !important;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
  width: 8px;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
  background: #ddd;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb:hover {
  background: #ddd;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.select2-container--default .select2-selection--single .select2-selection__arrow:after {
  position: absolute;
  right: 10px;
  top: 50%;
  content: "\f107";
  font-family: "Line Awesome Free";
  font-weight: 900;
  transition: .3s;
  transform: translateY(-50%);
}

.selection {
  width: 100%;
}

.select2-selection.select2-selection--single {
  background: hsl(var(--base-two));
  border-color: hsl(var(--base)) !important;
  color: hsl(var(--white));
  padding: 10px 24px !important;
  line-height: 1.2;
  height: 53px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 53px !important;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow:after {
  content: "\f106";
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: hsl(var(--white)/.7);
  line-height: 32px !important;
}

.select2-results__option:last-child {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.select2-results__option:first-child {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.select2-results__option.select2-results__option--selected,
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #f1f1f1 !important;
  color: #000 !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
  box-shadow: 0 0 25px rgba(115, 103, 240 0.071) !important;
  outline: 0 !important;
}

.select2-dropdown .country-flag {
  width: 25px;
  height: 25px;
  border-radius: 8px;
}

.select2-dropdown {
  background-color: hsl(var(--base-two)) !important;
}

.select2-dropdown .gateway-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 0px !important;
}

.select2-dropdown .gateway-subtitle {
  font-size: 12px;
  margin-bottom: 0px !important;
}

.select2-container--open .select2-selection.select2-selection--single,
.select2-container--open .select2-selection.select2-selection--multiple {
  border-color: hsl(var(--base)) !important;
  border-radius: .375rem !important;
}

.select2-results__option.select2-results__option--selected,
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: hsl(var(--white)/.3) !important;
  color: hsl(var(--white)) !important;
}

.select2-results__option.select2-results__option--selected,
.select2-results__option--selectable,
.select2-container--default .select2-results__option--disabled {
  border: 1px solid hsl(var(--white)/.1) !important;
}


.gateway-card {
  padding: 15px;
}

.payment-item__btn-text {
  color: #000;
}

.payment-item__btn__icon {
  color: #000;
}

.payment-card-title {
  padding: 13px 25px;
  text-align: center;
  background-color: rgb(var(--main));
  border-radius: 5px;
  border: 0;
  margin-bottom: 0px;
  color: #fff;
}

.payment-system-list {
  --thumb-width: 100px;
  --thumb-height: 40px;
  --radio-size: 12px;
  --border-color: #cccccf59;
  --hover-border-color: rgb(var(--main));
  background-color: rgb(var(--main));
  border-radius: 5px;
  height: 100%;

}


.payment-system-list.is-scrollable {
  max-height: min(388px, 70vh);
  overflow-x: auto;
  padding: 4px;
}

.payment-system-list.is-scrollable::-webkit-scrollbar {
  width: 5px;
}

.payment-system-list.is-scrollable::-webkit-scrollbar {
  width: 5px;

}

.payment-system-list.is-scrollable::-webkit-scrollbar-thumb {
  background-color: hsl(var(--base));
  border-radius: 10px;
}

.payment-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 18px;
  border: 1px solid #fff;
  transition: all 0.3s;
}

.payment-item:first-child {
  border-top-color: #fff;
  border-radius: 5px 5px 0 0;
}

.payment-item:has(.payment-item__radio:checked) {
  border-left: 3px solid hsl(var(--base));
  border-radius: 0px;
}

.payment-item__check {
  border: 3px solid transparent;
}

.payment-item:has(.payment-item__radio:checked) .payment-item__check {
  border: 3px solid hsl(var(--base));
}

.payment-item__info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: calc(100% - var(--thumb-width))
}

.payment-item__check {
  width: var(--radio-size);
  height: var(--radio-size);
  border: 1px solid hsl(var(--base)/.5);
  display: inline-block;
  border-radius: 100%;

}

.payment-item__name {
  padding-left: 10px;
  width: calc(100% - var(--radio-size));
  transition: all 0.3s;
}

.payment-item__thumb {
  width: var(--thumb-width);
  height: var(--thumb-height);
  text-align: right;
  padding-left: 10px;

  &:has(.text) {
    width: fit-content;
  }
}

.payment-item__thumb img {
  max-width: var(--thumb-width);
  max-height: var(--thumb-height);
  object-fit: cover;
}


.deposit-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.deposit-info__title {
  max-width: 50%;
  margin-bottom: 0px;
  text-align: left;
}

.deposit-info__input {
  max-width: 50%;
  text-align: right;
  width: 100%;
}

.deposit-info__input-select {
  border: 1px solid var(--border-color);
  width: 100%;
  border-radius: 5px;
  padding-block: 6px;
}

.deposit-info__input-group {
  border: 1px solid var(--border-color);
  border-radius: 5px;

  .deposit-info__input-group-text {
    align-self: center;
    padding-left: 5px;
  }

}

.deposit-info__input-group .form--control {
  padding: 5px;
  border: 0;
  height: 35px;
  text-align: right;
}

.deposit-info__input-group .form--control:focus {
  box-shadow: unset;
}

.info-text .text,
.deposit-info__input .text {
  font-size: 14px;

}

.deposit-info__title .text.has-icon {
  display: flex;
  align-items: center;
  gap: 5px
}

.total-amount {
  border-top: 1px solid var(--border-color);
}

.total-amount .deposit-info__title {
  font-weight: 600;
}

.payment-item__btn {
  border: 0;
  border-block: 1px solid var(--border-color);
  border-bottom: 0;
  background: hsl(var(--base));
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 13px 15px;
  font-weight: 500;
}

.payment-item:hover+.payment-item__btn {
  border-top-color: #fff;
}

button .spinner-border {
  --bs-spinner-width: 1.5rem;
  --bs-spinner-height: 1.5rem;
}

.header.menu-fixed .header__bottom,
.header__top,
.footer__top,
.second--bg,
.verification-code span,
.blog-card {
  background-color: hsl(var(--base-two)) !important;
}

.custom--accordion .accordion-button:not(.collapsed)::after,
.copied::after,
.custom--table thead th,
.cookies-card__icon,
.scroll-to-top .scroll-icon i {
  color: hsl(var(--base-two)) !important;
}

a:hover,
.header-register-btn,
.header-register-btn i,
.blog-card__meta li i {
  color: hsl(var(--base));
}

.header-login-btn {
  border: 1px solid hsl(var(--base)) !important;
}

.header .header__bottom .main-menu li a.active,
.page-breadcrumb li a:hover,
.text-list__item-icon {
  color: hsl(var(--base)) !important;
}

.testimonial-card__client .thumb {
  border: 3px solid hsl(var(--base));
}


.btn--base,
.roadmap-wrapper::after,
.single-roadmap .roadmap-dot,
.subscribe-form .subscribe-btn,
.scroll-to-top,
.custom--accordion .accordion-button:not(.collapsed),
.video-btn::before,
.video-btn::after,
.video-btn,
.loadbar,
.cursor,
.custom--file-upload::before,
.header .main-menu li .sub-menu,
.header .main-menu li .sub-menu li a::before,
.btn-outline--base:hover,
.btn--primary,
.qr-code-copy-form input[type="submit"],
.pagination .page-item.active .page-link,
.custom--bg,
.modal-header .btn-close:hover,
.preloader div:before,
.progress-bar {
  background-color: hsl(var(--base)) !important;
}

.single-roadmap:hover,
.custom--card:hover,
.d-widget:hover,
.blog-card:hover,
.verification-code span {
  border-color: hsl(var(--base)) !important;
}

.feature-card__icon i,
.testimonial-card::before,
.date-unit-list .single-unit span,
.page-breadcrumb li:first-child::before,
b.text-success,
.custom--cl,
.custom--accordion .accordion-button::after,
.contact-card__icon i,
.header .main-menu li a:hover,
.header .main-menu li a:focus,
.header .top-info a,
.text--base,
.d-widget__icon i,
.btn-outline--base,
.footer .footer-menu li a:hover,
.header .main-menu li.menu_has_children:hover>a::before {
  color: hsl(var(--base)) !important;
}

a.btn-outline--base:hover {
  color: #000 !important;
}

.header .main-menu li .sub-menu li a:hover {
  color: #000 !important;
}

.header-login-btn:hover {
  box-shadow: 0 0 5px 2px hsl(var(--base));
}

.feature-card,
.team-card {
  box-shadow: 0 0 0px 2px hsl(var(--base)) !important;
}

.feature-card:hover,
.team-card:hover,
.blog-card:hover {
  box-shadow: 0 5px 25px 2px hsl(var(--base)) !important;
}

.social-links li a:hover,
.input-group-text {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base));
}

.cursor-follower,
.btn-outline--base,
.blog-card {
  border: 1px solid hsl(var(--base)) !important;
}

.account-wrapper,
.header .main-menu li .sub-menu,
.deposit-preview-card,
.single-roadmap,
.testimonial-card,
.custom--card {
  border: 2px solid hsl(var(--base) / .3) !important;
}

.account-thumb-area .account-thumb,
.custom--accordion .accordion-item,
.custom--border,
.select,
.header-register-btn {
  border: 1px solid hsl(var(--base)) !important;
}

.form-control:placeholder-shown,
.country-code .input-group-prepend .input-group-text {
  border-color: rgba(255, 255, 255, 0.15) !important;
}

.form-control,
.form-control:focus {
  border-color: hsl(var(--base)) !important;
}

.roadmap-wrapper .single-roadmap:nth-child(even)::before {
  border-color: transparent transparent transparent hsl(var(--base)) !important;
}

.roadmap-wrapper .single-roadmap::before {
  border-color: transparent hsl(var(--base)) transparent transparent;
}

.roadmap-wrapper .single-roadmap:hover::before {
  border-color: transparent hsl(var(--base)) transparent transparent;
}

.custom--card:hover,
.header-register-btn:hover {
  box-shadow: 0 5px 20px 1px hsl(var(--base));
}

.glass--bg,
.single-roadmap,
.testimonial-slider .prev,
.testimonial-slider .next,
.contact-form,
.contact-card,
.d-widget,
.header-login-btn:hover,
.feature-card::before,
.team-card::after {
  background-color: hsl(var(--base))2b;
}

a.btn--base:hover,
.btn--base:hover,
[class*="btn--"]:not(.btn--link):not(.btn--light):not(.btn) {
  color: #000 !important;
}

.btn--base:hover {
  box-shadow: 0 2px 5px 1px hsl(var(--base) / .1);
}

.custom--table {
  background-color: hsl(var(--base) / .15);
}

.count-wrapper {
  border-color: hsl(var(--base));
  box-shadow: 0 0 15px 0px hsl(var(--base));
  --shadow-color: hsl(var(--base));
}

.testimonial-card:hover {
  border-color: hsl(var(--base)) !important;
}

.d-widget__amount::after {
  background-color: hsl(var(--base) / .15);
}

.d-widget:hover .d-widget__amount::after {
  background-color: hsl(var(--base));
}

.d-widget {
  border-color: hsl(var(--base));
}

.d-widget:hover {
  box-shadow: 0 3px 10px 1px hsl(var(--base) / .3);
}

.highlighted-text {
  background-color: hsl(var(--base));
  box-shadow: 0 0 5px 1px hsl(var(--base));
}


.phase-card .card-header:hover {
  box-shadow: 0 3px 10px 1px hsl(var(--base));
}

.profile-thumb .avatar-edit label {
  background-color: hsl(var(--base));
}

.bg--base,
.sidebar .widget-title::before,
.blog-details__thumb .post__date .date,
.copied::after,
.header-login-btn,
.header-register-btn:hover {
  background-color: hsl(var(--base)) !important;
}

.subscribe-wrapper,
.subscribe-form,
.contact-form,
.contact-card,
.phase-card .card-header {
  border-color: hsl(var(--base)) !important;
}

@supports (backdrop-filter: none) {

  .glass--bg,
  .single-roadmap,
  .testimonial-slider .prev,
  .testimonial-slider .next,
  .contact-form,
  .contact-card,
  .d-widget,
  .glass--bg-two,
  .testimonial-card,
  .subscribe-wrapper,
  .account-wrapper,
  .phase-card .card-header,
  .custom--card,
  .profile-form {
    background-color: hsl(var(--base) / .1) !important;
  }
}

@supports not (backdrop-filter: none) {

  .glass--bg,
  .single-roadmap,
  .testimonial-slider .prev,
  .testimonial-slider .next,
  .contact-form,
  .contact-card,
  .d-widget,
  .glass--bg-two,
  .testimonial-card,
  .subscribe-wrapper,
  .account-wrapper {
    background-color: hsl(var(--base-two)) !important;
  }
}


.video-btn:hover {
  color: black;
}

.custom--table thead th,
.single-roadmap .roadmap-dot::before,
.single-roadmap .roadmap-dot::after,
input[type="file"]::file-selector-button {
  background-color: hsl(var(--base)) !important;
}
.single-roadmap p {
  text-align: justify;
  hyphens: none;
  word-wrap: normal;
  overflow-wrap: normal;
}

.list-group-item {
  border-color: hsl(var(--base)) !important;
}

.card-empty {
  margin: 0 auto;
  text-align: center;
  padding: clamp(2rem, 1.391rem + 1.268vw, 2.5rem) clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
}

.card-empty img {
  height: 100px;
}

.card-empty .empty-thumb {
  text-align: center;
  margin-bottom: 10px;
}

/* Language */

.dropdown-lang {
  margin-left: 30px !important;
}

@media (max-width: 1199px) {
  .dropdown-lang {
    margin-left: 0px !important;
  }
}

.dropdown-lang .language-btn .flag {
  width: 20px;
  height: 15px;
  border-radius: 2px;
}

.dropdown-lang .language-btn::after {
  color: #fff !important;
}

.dropdown-lang .language-text {
  color: hsl(var(--white));
  font-size: 16px;
}

.dropdown-lang .dropdown-menu {
  width: 112px !important;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 10px !important;
  max-height: 370px;
  overflow-y: auto;
  background-color: hsl(var(--base-two));
  z-index: 999;
  box-shadow: var(--box-shadow);
  transform: inherit;
  min-width: 130px !important;
}

.dropdown-lang .dropdown-menu.show {
  opacity: 1 !important;
  visibility: visible !important;
}

.dropdown-lang .dropdown-menu a {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  color: hsl(var(--white)) !important;
  transition: 0.3s;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

@media (max-width: 575px) {
  .dropdown-lang .dropdown-menu a:last-child {
    border-bottom: none;
  }

  .dropdown-lang .dropdown-menu li {
    border-bottom: none !important;
  }
}

.dropdown-lang .dropdown-menu a:hover {
  color: hsl(var(--base));
}

.dropdown-lang .dropdown-menu li:last-child a {
  border-bottom: none;
  padding-bottom: 0px;
  margin-bottom: 0px;
}

.dropdown-lang .dropdown-menu a .flag {
  width: 20px;
  margin-right: 10px;
  height: 15px;
}

.dropdown-lang .dropdown-menu li:last-child a {
  margin-bottom: 0px;
}

.dropdown-lang .a {
  color: #fff !important;
}

@media (max-width: 575px) {
  .dropdown-lang .dropdown-menu a {
    padding: 0px !important;
  }
}

.auth-devide span {
  padding-inline: 6px;
  background: hsl(var(--base-two));
  color: hsl(var(--white));
}

.auth-devide::after {
  content: '';
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.211);
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  z-index: -1;
}

.social-login-btn {
  border: 1px solid hsl(var(--base));
  padding: 7px 16px;
  border-radius: 4px;
  width: 100%;
  display: flex;
  align-content: center;
  gap: 12px;
  justify-content: center;
  color: #fff;
  font-size: 15px;
  transition: all linear 0.3s;
}

.social-login-btn:hover {
  background-color: hsl(var(--base) / .1);
  color: #fff;
}

.social-login-btn img {
  width: 20px;
}

.social-auth {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.auth-devide {
  text-align: center;
  margin-block: 24px;
  position: relative;
  z-index: 1;
}

.select2 .dropdown-wrapper {
  display: none;
}

.select2-container:has(.select2-selection--single) {
  width: 100% !important;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-70 {
  padding-top: 70px;
}

.text-muted {
  color: #bcc9d4 !important;
}
.single-roadmap,
.single-roadmap p,
.roadmap-text {
  word-break: normal !important;
  overflow-wrap: normal !important;
  white-space: normal !important;
  hyphens: none !important;
  text-align: justify !important; /* optional if you want justification */
}
