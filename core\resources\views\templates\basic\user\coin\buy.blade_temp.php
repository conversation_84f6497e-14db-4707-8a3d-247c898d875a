@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="container" style="margin-top: -75px;">
        <div class="row justify-content-center">
            <div class="col-lg-9">
                <form action="{{ route('user.coin.purchase') }}" method="post" class="purchase-form">
                    @csrf
                    <input type="hidden" name="currency">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="payment-system-list">
                                <div class="payment-system-list__header">
                                    <h4 class="title">@lang('Select Payment Method')</h4>
                                </div>
                                <div class="payment-system-list__body">
                                    @foreach ($gateways as $gateway)
                                        <div class="payment-item gateway-option" data-gateway="{{ json_encode($gateway) }}">
                                            <div class="payment-item__info">
                                                <span class="payment-item__check"></span>
                                                <span class="payment-item__name">{{ __($gateway->name) }}</span>
                                            </div>
                                            <div class="payment-item__thumb">
                                                <img src="{{ getImage(getFilePath('gateway') . '/' . $gateway->image, getFileSize('gateway')) }}" alt="@lang('payment-method')">
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6" style="display: flex; flex-direction: column; min-height: 550px;">
                            <div class="payment-system-list p-3" style="flex-grow: 1;">
                                <div class="purchase-info">
                                    <div class="purchase-info__title">
                                        <p class="text mb-0">@lang('Amount in USD')</p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <div class="purchase-info__input-group input-group">
                                            <span class="purchase-info__input-group-text">$</span>
                                            <input type="text" class="form-control form--control amount"
                                                name="amount" placeholder="@lang('00.00')"
                                                value="{{ old('amount') }}" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="purchase-info">
                                    <div class="purchase-info__title">
                                        <p class="text has-icon"> @lang('Exchange Limits in USD')</p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <p class="text"><span class="gateway-limit">@lang('0.00')</span></p>
                                    </div>
                                </div>
                                <div class="purchase-info">
                                    <div class="purchase-info__title">
                                        <p class="text has-icon">@lang('Processing Charge')
                                            <span data-bs-toggle="tooltip" title="@lang('Processing charge for payment gateways')"
                                                class="proccessing-fee-info"><i class="las la-info-circle"></i>
                                            </span>
                                        </p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <p class="text">$<span class="processing-fee">@lang('0.00')</span>
                                        </p>
                                    </div>
                                </div>

                                <div class="purchase-info total-amount py-2 mt-2">
                                    <div class="purchase-info__title">
                                        <p class="text">@lang('Total in USD')</p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <div class="purchase-info__input-group input-group">
                                            <span class="purchase-info__input-group-text">$</span>
                                            <span class="form-control form--control text-end final-amount" style="min-height: 38px;">@lang('0.00')</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="purchase-info gateway-conversion d-none total-amount pt-2">
                                    <div class="purchase-info__title">
                                        <p class="text">@lang('Conversion')
                                        </p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <p class="text"></p>
                                    </div>
                                </div>
                                <div class="purchase-info conversion-currency d-none total-amount pt-2">
                                    <div class="purchase-info__title">
                                        <p class="text">
                                            @lang('In') <span class="gateway-currency"></span>
                                        </p>
                                    </div>
                                    <div class="purchase-info__input">
                                        <p class="text">
                                            <span class="in-currency"></span>
                                        </p>
                                    </div>
                                </div>

                                <!-- Important Disclaimer -->
                                <div class="purchase-disclaimer mb-3" style="padding: 1rem; background-color: #fff3cd; border-radius: 0.5rem; border: 1px solid #ffeeba; margin-top: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <div style="display: flex; align-items: flex-start;">
                                        <i class="fas fa-exclamation-triangle me-3 mt-1" style="color: #856404; font-size: 1.1rem;"></i>
                                        <div>
                                            <h6 style="font-weight: 700; color: #856404; margin-bottom: 0.1rem; font-size: 1rem;">Important Notice</h6>
                                        </div>
                                    </div>

                                    <!-- Acknowledgment Checkbox -->
                                    <div class="form-check mt-3" style="padding-left: 1.5rem; border-top: 1px solid rgba(133, 100, 4, 0.2); padding-top: 0.0rem; margin-left: 0.4rem; margin-right: 0.5rem;">
                                        <input class="form-check-input custom-checkbox" type="checkbox" id="acknowledgeTerms" style="cursor: pointer; width: 1.25rem; height: 1.25rem; border: 2px solid #856404;">
                                        <label class="form-check-label" for="acknowledgeTerms" style="font-size: 0.95rem; color: #856404; cursor: pointer; font-weight: 500; padding-left: 0.5rem; padding-bottom: 0rem; line-height: 1.3;">
                                            I understand and agree that my payment is non-refundable and can only be used to acquire FOC.
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn--base w-100 mt-4 confirm-purchase-btn" disabled>
                                    <span>@lang('Acquire FOC Now')</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .purchase-info__input-group .form--control {
            background-color: transparent !important;
        }

        /* Make the dollar sign in Total row match the Amount row */
        .purchase-info__input-group-text {
            background-color: #e9ecef !important;
            border: 1px solid #ced4da !important;
            border-radius: 0.25rem 0 0 0.25rem !important;
        }

        /* Make font sizes consistent between labels and values */
        .purchase-info__title .text,
        .purchase-info__input .text,
        .purchase-info__input .gateway-limit,
        .purchase-info__input .processing-fee,
        .purchase-info__input .final-amount,
        .purchase-info__input .in-currency,
        .purchase-info__input .rate,
        .purchase-info__input .method_currency {
            font-size: 1rem !important;
        }

        /* Make Total slightly larger for emphasis */
        .total-amount .purchase-info__title .text,
        .total-amount .purchase-info__input .text,
        .total-amount .purchase-info__input .final-amount {
            font-size: 1.1rem !important;
            font-weight: 500;
        }

        /* Ensure the dollar sign in Total row has the same size as Amount row */
        .total-amount .purchase-info__input-group-text {
            font-size: 1rem !important;
            font-weight: 400;
        }

        /* Improve vertical alignment and text alignment */
        .purchase-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .purchase-info__title,
        .purchase-info__input {
            display: flex;
            align-items: center;
        }

        .purchase-info__title {
            text-align: left;
        }

        .purchase-info__input {
            text-align: right;
        }

        .purchase-info__title .text,
        .purchase-info__input .text {
            margin-bottom: 0;
        }

        /* Style the Confirm Purchase button */
        .confirm-purchase-btn {
            color: white !important;
            font-weight: 800 !important;
            font-size: 1.6rem !important;
            transition: all 0.3s ease;
            text-align: center !important;
            line-height: 1 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            height: auto !important;
            border: 2px solid transparent !important;
            transition: background-color 0.3s, transform 0.3s, border-color 0.3s !important;
        }

        /* Ensure the button text is properly centered */
        .confirm-purchase-btn span {
            display: inline-block;
            line-height: 1;
            padding: 0;
            margin: 0;
        }

        .confirm-purchase-btn:hover:not(:disabled) {
            transform: scale(1.02);
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
        }

        .confirm-purchase-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #6c757d !important;
        }

        /* Improve the disclaimer box appearance */
        .purchase-disclaimer {
            transition: all 0.3s ease;
        }

        .purchase-disclaimer:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }
    </style>
@endpush

@push('script')
    <script>
        'use strict';
        let gateway;
        let amount;
        let minAmount;
        let maxAmount;
        let amountValid = false;

        $(document).ready(function() {
            // Initialize terms checkbox handler
            $('#acknowledgeTerms').on('change', function() {
                checkSubmitButtonStatus();
            });

            // Initialize amount input handler
            $('.amount').on('input', function() {
                amount = parseFloat($(this).val()) || 0;
                checkAmountValidity();
                calculation();
            });

            // Initialize gateway selection
            $('.gateway-option').on('click', function() {
                $('.gateway-option').removeClass('active');
                $(this).addClass('active');
                gateway = $(this).data('gateway');
                minAmount = parseFloat(gateway.min_amount);
                maxAmount = parseFloat(gateway.max_amount);
                checkAmountValidity();
                calculation();
            });
        });

        function checkAmountValidity() {
            if (!gateway || !amount) {
                amountValid = false;
                return;
            }
            
            amountValid = amount >= minAmount && amount <= maxAmount;
            
            if (amountValid) {
                $('.amount').removeClass('is-invalid').addClass('is-valid');
            } else {
                $('.amount').removeClass('is-valid').addClass('is-invalid');
            }
        }

        function checkSubmitButtonStatus() {
            let termsAcknowledged = $('#acknowledgeTerms').is(':checked');
            
            if (amountValid && termsAcknowledged && gateway) {
                $(".purchase-form button[type=submit]").removeAttr('disabled');
            } else {
                $(".purchase-form button[type=submit]").attr('disabled', true);
            }
        }

        function calculation() {
            if (!gateway) return;
            
            // Extract just the numeric values from the min and max amounts
            let formattedMin = String(minAmount).replace(/[^\d.]/g, '');
            let formattedMax = String(maxAmount).replace(/[^\d.]/g, '');

            // Format with a single dollar sign
            $(".gateway-limit").html("$" + formattedMin + " - $" + formattedMax);

            let percentCharge = 0;
            let fixedCharge = 0;
            let totalPercentCharge = 0;

            if (amount) {
                percentCharge = parseFloat(gateway.percent_charge);
                fixedCharge = parseFloat(gateway.fixed_charge);
                totalPercentCharge = parseFloat(amount / 100 * percentCharge);
            }

            let totalCharge = parseFloat(totalPercentCharge + fixedCharge);
            let totalAmount = parseFloat((amount || 0) + totalPercentCharge + fixedCharge);

            $(".final-amount").text(totalAmount.toFixed(2));
            $(".processing-fee").text(totalCharge.toFixed(2));
            $("input[name=currency]").val(gateway.currency);
            $(".gateway-currency").text(gateway.currency);

            // Check both amount validation and terms acknowledgment
            checkSubmitButtonStatus();

            if (gateway.currency != "{{ gs('cur_text') }}" && gateway.method.crypto != 1) {
                $(".gateway-conversion, .conversion-currency").removeClass('d-none');
                $(".gateway-conversion").find('.purchase-info__input .text').html(
                    `1 {{ __(gs('cur_text')) }} = <span class="rate">${parseFloat(gateway.rate).toFixed(2)}</span>  <span class="method_currency">${gateway.currency}</span>`
                );
                $('.in-currency').text(parseFloat(totalAmount * gateway.rate).toFixed(gateway.method.crypto == 1 ? 8 : 2))
            } else {
                $(".gateway-conversion, .conversion-currency").addClass('d-none');
            }
        }
    </script>
@endpush
