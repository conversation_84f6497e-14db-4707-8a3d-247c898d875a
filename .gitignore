# Laravel specific
core/vendor/
core/node_modules/
core/storage/logs/
core/storage/framework/cache/
core/storage/framework/sessions/
core/storage/framework/views/
core/storage/app/public/
core/storage/debugbar/
core/bootstrap/cache/
core/temp/
core/.env
core/.env.*
core/.phpunit.result.cache
core/Homestead.json
core/Homestead.yaml
core/npm-debug.log
core/yarn-error.log
core/composer.lock

# IDE and Editor files (keep .vscode/settings.json for team)
.vscode/launch.json
.vscode/tasks.json
.vscode/*.code-workspace
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.cache

# Database files
*.sqlite
*.db

# Backup files
*.bak
*.backup
*_backup.*

# Sensitive files
core/config/database.php.backup
install/database.sql.backup

# Development tools
test_db.php
import_db.php
check_tables.php
create_sessions.php

# Composer
core/composer.lock

# NPM
package-lock.json
yarn.lock

# Application specific
core/storage/debugbar/
core/temp/
core/public/storage

# Documentation (keep localhost.md but ignore others)
# localhost.md is intentionally not ignored as it's important for setup
