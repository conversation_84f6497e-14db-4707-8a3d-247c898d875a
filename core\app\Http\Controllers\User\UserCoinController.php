<?php

namespace App\Http\Controllers\User;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\CoinHistory;
use App\Models\Phase;
use App\Models\Transaction;
use App\Models\GatewayCurrency;
use App\Models\Deposit;
use Illuminate\Http\Request;

class UserCoinController extends Controller
{
    public function index()
    {
        $pageTitle = 'FOC Purchase History';
        $coinHistories = CoinHistory::where('user_id', auth()->id())->orderBy('id', 'desc')->paginate(getPaginate());
        return view('Template::user.coin.index', compact('pageTitle', 'coinHistories'));
    }

    public function buy()
    {
        $pageTitle = 'Buy FOC Tokens';
        $gateways = GatewayCurrency::whereHas('method', function ($gate) {
            $gate->where('status', Status::ENABLE);
        })->with('method')->orderby('method_code')->get();
        
        return view('Template::user.coin.buy', compact('pageTitle', 'gateways'));
    }

    public function buyConfirm(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|gt:0',
        ]);

        $general = gs();
        $user = auth()->user();
        $amount = $request->amount;

        $phase = Phase::whereDate('start_date', '<=', now())
                      ->whereDate('end_date', '>=', now())
                      ->where('status', Status::ENABLE)
                      ->first();

        if (!$phase) {
            $notify[] = ['error', 'Sorry! There is no active FOC token sale phase currently available.'];
            return back()->withNotify($notify);
        }

        if ($phase->supply_type == Status::LIMITED_SUPPLY && $phase->coin_token <= 0) {
            $notify[] = ['error', 'Sorry! The current FOC token phase is sold out.'];
            return back()->withNotify($notify);
        }

        if ($user->balance < $amount) {
            $notify[] = ['error', 'Insufficient balance for this purchase.'];
            return back()->withNotify($notify);
        }

        $quantity = $amount / $phase->price;

        if ($phase->supply_type == Status::LIMITED_SUPPLY && $phase->coin_token < $quantity) {
            $notify[] = ['error', 'Sorry! Only ' . $phase->coin_token . ' ' . $general->coin_text . ' tokens are available in this phase.'];
            return back()->withNotify($notify);
        }

        $user->balance -= $amount;
        $user->save();

        $transaction = new Transaction();
        $transaction->user_id = $user->id;
        $transaction->amount = $amount;
        $transaction->post_balance = $user->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->details = 'FOC token purchase';
        $transaction->trx = getTrx();
        $transaction->remark = 'foc_purchase';
        $transaction->save();

        $coinHistory = new CoinHistory();
        $coinHistory->user_id = $user->id;
        $coinHistory->phase_id = $phase->id;
        $coinHistory->amount = $amount;
        $coinHistory->quantity = $quantity;
        $coinHistory->price = $phase->price;
        $coinHistory->save();

        if ($phase->supply_type == Status::LIMITED_SUPPLY) {
            $phase->coin_token -= $quantity;
            $phase->save();
        }

        $notify[] = ['success', $quantity . ' ' . $general->coin_text . ' ' . 'successfully purchased'];
        return to_route('user.coin.index')->withNotify($notify);
    }

    public function focPurchaseInsert(Request $request)
    {
        $request->validate([
            'amount'   => 'required|numeric|gt:0',
            'gateway'  => 'required',
            'currency' => 'required',
        ]);

        $user = auth()->user();
        $gate = GatewayCurrency::whereHas('method', function ($gateMethod) {
            $gateMethod->where('status', Status::ENABLE);
        })->where('method_code', $request->gateway)
          ->where('currency', $request->currency)
          ->first();

        if (!$gate) {
            $notify[] = ['error', 'Invalid gateway selected.'];
            return back()->withNotify($notify);
        }

        $phase = Phase::whereDate('start_date', '<=', now())
                      ->whereDate('end_date', '>=', now())
                      ->where('status', Status::ENABLE)
                      ->first();

        if (!$phase) {
            $notify[] = ['error', 'Sorry! There is no active FOC token sale phase currently available.'];
            return back()->withNotify($notify);
        }

        if ($phase->supply_type == Status::LIMITED_SUPPLY && $phase->coin_token <= 0) {
             $notify[] = ['error', 'Sorry! The current FOC token phase is sold out.'];
             return back()->withNotify($notify);
        }

        if ($gate->min_amount > $request->amount || $gate->max_amount < $request->amount) {
            $notify[] = ['error', 'Please follow the purchase limit for the selected gateway.'];
            return back()->withNotify($notify);
        }

        $charge      = $gate->fixed_charge + ($request->amount * $gate->percent_charge / 100);
        $payable     = $request->amount + $charge;
        
        if (strtoupper($gate->currency) == 'USD') {
            $finalAmount = $payable;
        } else {
            $finalAmount = $payable * $gate->rate;
        }

        $deposit                  = new Deposit();
        $deposit->user_id         = $user->id;
        $deposit->method_code     = $gate->method_code;
        $deposit->method_currency = strtoupper($gate->currency);
        $deposit->amount          = $request->amount;
        $deposit->charge          = $charge;
        $deposit->rate            = $gate->rate;
        $deposit->final_amount    = $finalAmount;
        $deposit->btc_amount      = 0;
        $deposit->btc_wallet      = "";
        $deposit->trx             = getTrx();
        $deposit->status          = Status::PAYMENT_INITIATE;
        $deposit->detail          = [
            'is_foc_purchase'       => true,
            'phase_id'              => $phase->id,
            'foc_price_at_purchase' => $phase->price,
            'coin_text'             => gs()->coin_text ?? 'FOC',
        ];
        $deposit->save();

        session()->put('Track', $deposit->trx);
        return to_route('user.deposit.confirm');
    }
}
