<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BypassLicensing
{
    /**
     * Handle an incoming request.
     * This middleware bypasses all licensing checks for localhost development.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Simply pass through without any licensing checks
        return $next($request);
    }
}
