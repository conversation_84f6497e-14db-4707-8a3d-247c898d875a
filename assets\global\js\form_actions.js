<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['log_message'])) {
    $log_file = __DIR__ . '/form_actions_debug.log';
    file_put_contents($log_file, $_POST['log_message'] . "\n", FILE_APPEND);
    exit;
}
?>
(function($){
    "use strict"

    $('[name=form_type]').on('change',function(){
        var formType = $(this).val();
        var extraFields = formGenerator.extraFields(formType);
        $('.extra_area').html(extraFields);
        $('.extra_area').find('select').select2({
            dropdownParent: $('#formGenerateModal')
        });
    }).change();
    
    $(document).on('click','.addOption',function(){
        var html = formGenerator.addOptions();
        $('.options').append(html);
    });
    
    $(document).on('click','.removeOption',function(){
        $(this).closest('.form-group').remove();
    });
    
    $(document).on('click','.editFormData',function () {
        formGenerator.formEdit($(this));
        $('.extra_area').find('select').select2({
            dropdownParent: $('#formGenerateModal')
        });
        
    });
    
    $(document).on('click','.removeFormData',function () {
        $(this).closest('.form-field-wrapper').remove();
        $('.submitRequired').removeClass('d-none');
    });
    
    $('.form-generate-btn').on('click',function(){
        formGenerator.showModal();
    });
    
    
    var updateId = formGenerator.totalField;
    $(formGenerator.formClassName).on('submit',function (e) {
        updateId += 1;
        e.preventDefault();
        var form = $(this);
        var formItem = formGenerator.formsToJson(form);
        formGenerator.makeFormHtml(formItem,updateId);
        formGenerator.closeModal();
        $('.submitRequired').removeClass('d-none');
    });
})(jQuery)












