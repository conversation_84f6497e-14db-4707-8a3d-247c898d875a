<?php

namespace App\Http\Controllers\Gateway\Moonpay;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\PaymentController;
use App\Models\Deposit;
use Illuminate\Http\Request;
use App\Models\AdminNotification;

class ProcessController extends Controller
{
    public static function process($deposit)
    {
        $moonPayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);

        $send = [
            'apiKey' => $moonPayAcc->public_key->value ?? '',
            'environment' => $moonPayAcc->mode->value ?? 'sandbox',
            'currency' => $deposit->method_currency,
            'amount' => round($deposit->final_amount, 2),
            'cryptoCurrency' => $moonPayAcc->default_crypto->value ?? 'btc',
            'transactionId' => $deposit->trx,
            'walletAddress' => $moonPayAcc->wallet_address->value ?? '',
            'view' => 'user.payment.Moonpay'
        ];

        return json_encode($send);
    }

    public function ipn(Request $request)
    {
        $signature = $request->header('moonpay-signature');
        $payload = $request->getContent();
        
        if (!$signature || !$payload) {
            return response()->json(['error' => 'Missing signature or payload'], 400);
        }

        $deposit = Deposit::where('trx', $request->transactionId)
            ->where('status', Status::PAYMENT_INITIATE)
            ->orderBy('id', 'DESC')
            ->first();

        if (!$deposit) {
            return response()->json(['error' => 'Invalid deposit'], 400);
        }

        $moonPayAcc = json_decode($deposit->gatewayCurrency()->gateway_parameter);
        
        // Verify webhook signature
        $expectedSignature = hash_hmac('sha256', $payload, $moonPayAcc->secret_key->value);
        
        if (!hash_equals($signature, $expectedSignature)) {
            $adminNotification = new AdminNotification();
            $adminNotification->user_id = 0;
            $adminNotification->title = 'MoonPay webhook signature verification failed';
            $adminNotification->click_url = '#';
            $adminNotification->save();
            
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        $webhookData = json_decode($payload, true);
        
        // Store webhook data in deposit details
        $deposit->detail = $webhookData;
        $deposit->save();

        // Process based on transaction status
        if ($webhookData['status'] === 'completed' && 
            $webhookData['amount'] == $deposit->final_amount && 
            $deposit->status == Status::PAYMENT_INITIATE) {
            
            PaymentController::userDataUpdate($deposit);
            
            return response()->json(['success' => true]);
        }

        return response()->json(['error' => 'Payment pending or failed'], 400);
    }
}



