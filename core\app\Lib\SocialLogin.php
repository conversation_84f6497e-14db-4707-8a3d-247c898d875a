<?php

namespace App\Lib;

use App\Constants\Status;
use App\Models\AdminNotification;
use App\Models\User;
use App\Models\UserLogin;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Socialite;

class SocialLogin
{
    private $provider;
    private $fromApi;

    public function __construct($provider, $fromApi = false)
    {
        $this->provider = $provider;
        $this->fromApi = $fromApi;
        
        // Register custom LinkedIn provider
        if ($provider === 'linkedin') {
            Socialite::extend('linkedin', function ($app) {
                $config = $app['config']['services.linkedin'];
                return new CustomLinkedInProvider(
                    $app['request'],
                    $config['client_id'],
                    $config['client_secret'],
                    $config['redirect']
                );
            });
        }
        
        $this->configuration();
    }

    public function redirectDriver()
    {
        try {
            $socialiteProvider = ($this->provider === 'x') ? 'twitter' : $this->provider;
            $driver = Socialite::driver($socialiteProvider);
            
            // LinkedIn specific configuration
            if ($socialiteProvider === 'linkedin') {
                return $driver->with([
                    'scope' => 'openid profile email',
                ])->redirect();
            }
            
            return $driver->redirect();
        } catch (\Exception $e) {
            Log::error('Social login redirect error: ' . $e->getMessage(), [
                'provider' => $this->provider,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function configuration()
    {
        try {
            $provider = $this->provider;
            $credentials = gs('socialite_credentials');
            
            $socialiteProvider = ($provider === 'x') ? 'twitter' : $provider;
            $credentialKey = $provider;
            
            if (!isset($credentials->$credentialKey)) {
                throw new Exception(ucfirst($provider) . ' credentials not found');
            }

            $configuration = $credentials->$credentialKey;

            switch ($socialiteProvider) {
                case 'twitter':
                    Config::set('services.twitter', [
                        'client_id'     => $configuration->client_id,
                        'client_secret' => $configuration->client_secret,
                        'redirect'      => route('user.social.login.callback', $provider),
                        'use_oauth_2'   => true,
                    ]);
                    break;
                    
                case 'linkedin':
                    Config::set('services.linkedin', [
                        'client_id'     => $configuration->client_id,
                        'client_secret' => $configuration->client_secret,
                        'redirect'      => route('user.social.login.callback', $provider),
                        // Add these specific settings for LinkedIn
                        'scope' => ['openid', 'profile', 'email'],
                    ]);
                    break;
                    
                default:
                    Config::set('services.' . $socialiteProvider, [
                        'client_id'     => $configuration->client_id,
                        'client_secret' => $configuration->client_secret,
                        'redirect'      => route('user.social.login.callback', $provider),
                    ]);
            }
        } catch (\Exception $e) {
            Log::error('Social login configuration error: ' . $e->getMessage(), [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function login()
    {
        try {
            // Map 'x' provider to 'twitter' for Socialite
            $socialiteProvider = ($this->provider === 'x') ? 'twitter' : $this->provider;
            $driver = Socialite::driver($socialiteProvider);
            
            if ($this->fromApi) {
                try {
                    $user = (object)$driver->userFromToken(request()->token)->user;
                } catch (\Throwable $th) {
                    Log::error('Social login API error: ' . $th->getMessage());
                    throw new Exception('Something went wrong');
                }
            } else {
                $user = $driver->user();
            }

            if($this->provider == 'linkedin-openid') {
                $user->id = $user->sub;
            }

            $userData = User::where('provider_id', $user->id)->first();

            if (!$userData) {
                if (!gs('registration')) {
                    throw new Exception('New account registration is currently disabled');
                }
                $emailExists = User::where('email', @$user->email)->exists();
                if ($emailExists) {
                    throw new Exception('Email already exists');
                }

                $userData = $this->createUser($user, $this->provider);
            }
            if ($this->fromApi) {
                $tokenResult = $userData->createToken('auth_token')->plainTextToken;
                $this->loginLog($userData);
                return [
                    'user'         => $userData,
                    'access_token' => $tokenResult,
                    'token_type'   => 'Bearer',
                ];
            }
            Auth::login($userData);
            $this->loginLog($userData);
            $redirection = Intended::getRedirection();
            return $redirection ? $redirection : to_route('user.home');
        } catch (\Exception $e) {
            Log::error('LinkedIn login process error: ' . $e->getMessage());
            throw $e;
        }
    }

    private function createUser($user, $provider)
    {
        $general  = gs();
        $password = getTrx(8);

        $firstName = null;
        $lastName = null;

        if (@$user->first_name) {
            $firstName = $user->first_name;
        }
        if (@$user->last_name) {
            $lastName = $user->last_name;
        }

        if ((!$firstName || !$lastName) && @$user->name) {
            $firstName = preg_replace('/\W\w+\s*(\W*)$/', '$1', $user->name);
            $pieces    = explode(' ', $user->name);
            $lastName  = array_pop($pieces);
        }

        $referBy = session()->get('reference');
        if ($referBy) {
            $referUser = User::where('username', $referBy)->first();
        } else {
            $referUser = null;
        }

        $newUser = new User();
        $newUser->provider_id = $user->id;

        $newUser->email = $user->email;

        $newUser->password = Hash::make($password);
        $newUser->firstname = $firstName;
        $newUser->lastname = $lastName;
        $user->ref_by    = $referUser ? $referUser->id : 0;

        $newUser->status = Status::VERIFIED;
        $newUser->kv = $general->kv ? Status::NO : Status::YES;
        $newUser->ev = Status::VERIFIED;
        $newUser->sv = gs('sv') ? Status::UNVERIFIED : Status::VERIFIED;
        $newUser->ts = Status::DISABLE;
        $newUser->tv = Status::VERIFIED;
        $newUser->provider = $provider;
        $newUser->save();

        $adminNotification = new AdminNotification();
        $adminNotification->user_id = $newUser->id;
        $adminNotification->title = 'New member registered';
        $adminNotification->click_url = urlPath('admin.users.detail', $newUser->id);
        $adminNotification->save();

        $user = User::find($newUser->id);

        return $user;
    }

    private function loginLog($user)
    {
        //Login Log Create
        $ip = getRealIP();
        $exist = UserLogin::where('user_ip', $ip)->first();
        $userLogin = new UserLogin();

        //Check exist or not
        if ($exist) {
            $userLogin->longitude =  $exist->longitude;
            $userLogin->latitude =  $exist->latitude;
            $userLogin->city =  $exist->city;
            $userLogin->country_code = $exist->country_code;
            $userLogin->country =  $exist->country;
        } else {
            $info = json_decode(json_encode(getIpInfo()), true);
            $userLogin->longitude =  @implode(',', $info['long']);
            $userLogin->latitude =  @implode(',', $info['lat']);
            $userLogin->city =  @implode(',', $info['city']);
            $userLogin->country_code = @implode(',', $info['code']);
            $userLogin->country =  @implode(',', $info['country']);
        }

        $userAgent = osBrowser();
        $userLogin->user_id = $user->id;
        $userLogin->user_ip =  $ip;

        $userLogin->browser = @$userAgent['browser'];
        $userLogin->os = @$userAgent['os_platform'];
        $userLogin->save();
    }
}












