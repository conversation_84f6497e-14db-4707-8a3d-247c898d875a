<?php

namespace App\Http\Controllers\Gateway\Bitgo\Support;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BitgoAPI
{
    private $apiKey;
    private $walletId;
    private $baseUrl;
    private $coin;

    /**
     * Initialize BitGo API
     * 
     * @param string $apiKey BitGo API key
     * @param string $walletId Wallet ID in format "currency:id"
     * @param string $environment "test" or "prod"
     */
    public function __construct($apiKey, $walletId, $environment = 'test')
    {
        $this->apiKey = $apiKey;
        $this->walletId = $walletId;
        
        // Extract coin from wallet ID (format: "btc:walletid")
        $parts = explode(':', $walletId);
        $this->coin = strtolower($parts[0]);
        
        // Set base URL based on environment
        $this->baseUrl = $environment === 'prod' 
            ? 'https://app.bitgo.com/api/v2' 
            : 'https://app.test.bitgo.com/api/v2';
    }

    /**
     * Create a new transaction
     * 
     * @param float $amount Amount to be paid
     * @param string $currency Currency code
     * @param string $externalId External transaction ID
     * @return array Response with address and other details
     */
    public function createTransaction($amount, $currency, $externalId)
    {
        try {
            // Log the request details
            Log::info('BitGo creating address', [
                'wallet_id' => $this->walletId,
                'coin' => $this->coin,
                'external_id' => $externalId,
                'url' => $this->baseUrl . '/' . $this->coin . '/wallet/' . $this->walletId . '/address'
            ]);
            
            // Create a new address for receiving payment
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/' . $this->coin . '/wallet/' . $this->walletId . '/address', [
                'label' => 'Payment ' . $externalId,
            ]);
            
            // Log the full response for debugging
            Log::debug('BitGo address creation response', [
                'status' => $response->status(),
                'body' => $response->body(),
                'headers' => $response->headers()
            ]);
            
            if (!$response->successful()) {
                // If creation failed because address already exists, try to get existing addresses
                if ($response->status() === 400 && strpos($response->body(), 'already exists') !== false) {
                    Log::info('Address already exists, fetching existing addresses', [
                        'external_id' => $externalId
                    ]);
                    
                    // Try to get existing addresses with this label
                    $existingResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $this->apiKey,
                        'Content-Type' => 'application/json',
                    ])->get($this->baseUrl . '/' . $this->coin . '/wallet/' . $this->walletId . '/addresses', [
                        'limit' => 50,
                        'mine' => true
                    ]);
                    
                    if ($existingResponse->successful()) {
                        $addresses = $existingResponse->json()['addresses'] ?? [];
                        foreach ($addresses as $addr) {
                            if (isset($addr['label']) && $addr['label'] === 'Payment ' . $externalId) {
                                return [
                                    'success' => true,
                                    'address' => $addr['address'],
                                    'amount' => $amount,
                                    'currency' => $currency,
                                    'transaction_id' => $externalId,
                                    'wallet_id' => $this->walletId
                                ];
                            }
                        }
                    }
                }
                
                Log::error('BitGo address creation failed', [
                    'response_body' => $response->body(),
                    'status' => $response->status(),
                    'wallet_id' => $this->walletId,
                    'coin' => $this->coin,
                    'url' => $this->baseUrl . '/' . $this->coin . '/wallet/' . $this->walletId . '/address'
                ]);
                return [
                    'success' => false,
                    'message' => 'Failed to create address: ' . ($response->body())
                ];
            }
            
            $addressData = $response->json();
            
            return [
                'success' => true,
                'address' => $addressData['address'],
                'amount' => $amount,
                'currency' => $currency,
                'transaction_id' => $externalId,
                'wallet_id' => $this->walletId
            ];
            
        } catch (\Exception $e) {
            Log::error('BitGo API exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => 'API Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get transaction status
     * 
     * @param string $transactionDetails JSON string with transaction details
     * @return string Status of the transaction
     */
    public function getTransactionStatus($transactionDetails)
    {
        try {
            $details = json_decode($transactionDetails, true);
            $address = $details['address'] ?? null;
            
            if (!$address) {
                return 'unknown';
            }
            
            // Check for transfers to this address
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->get($this->baseUrl . '/' . $this->coin . '/wallet/' . $this->walletId . '/transfers', [
                'address' => $address
            ]);
            
            if (!$response->successful()) {
                return 'error';
            }
            
            $transfers = $response->json()['transfers'] ?? [];
            
            if (empty($transfers)) {
                return 'waiting';
            }
            
            // Check if any transfer is confirmed
            foreach ($transfers as $transfer) {
                if ($transfer['state'] === 'confirmed') {
                    return 'completed';
                } else if ($transfer['state'] === 'pending') {
                    return 'pending';
                }
            }
            
            return 'waiting';
            
        } catch (\Exception $e) {
            Log::error('BitGo status check exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 'error';
        }
    }

    /**
     * Get current exchange rate for a cryptocurrency from BitGo
     * 
     * @param string $crypto Cryptocurrency code (BTC, ETH, etc.)
     * @param string $fiat Fiat currency code (USD, EUR, etc.)
     * @return float|null Current exchange rate or null on failure
     */
    public function getExchangeRate($crypto = null, $fiat = 'USD')
    {
        try {
            // Use the coin from constructor if not specified
            $crypto = $crypto ?? $this->coin;
            
            // BitGo provides market data including exchange rates
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->get($this->baseUrl . '/market/latest');
            
            if (!$response->successful()) {
                Log::error('BitGo market data fetch failed', [
                    'response' => $response->json(),
                    'status' => $response->status()
                ]);
                return null;
            }
            
            $marketData = $response->json();
            
            // Find the exchange rate for the specified crypto and fiat pair
            foreach ($marketData['marketData'] ?? [] as $market) {
                if (strtolower($market['coin']) === strtolower($crypto) && 
                    strtolower($market['currency']) === strtolower($fiat)) {
                    return $market['rate'];
                }
            }
            
            // If not found in BitGo, fall back to CoinGecko as a backup
            Log::info('Exchange rate not found in BitGo, falling back to CoinGecko');
            return $this->getExchangeRateFromCoinGecko($crypto, $fiat);
            
        } catch (\Exception $e) {
            Log::error('BitGo exchange rate exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Try fallback on exception
            return $this->getExchangeRateFromCoinGecko($crypto, $fiat);
        }
    }

    /**
     * Fallback method to get exchange rates from CoinGecko
     */
    private function getExchangeRateFromCoinGecko($crypto, $fiat = 'USD')
    {
        try {
            $response = Http::get('https://api.coingecko.com/api/v3/simple/price', [
                'ids' => $this->mapCryptoToId($crypto),
                'vs_currencies' => strtolower($fiat)
            ]);
            
            if (!$response->successful()) {
                return null;
            }
            
            $data = $response->json();
            $cryptoId = $this->mapCryptoToId($crypto);
            
            if (isset($data[$cryptoId][strtolower($fiat)])) {
                return $data[$cryptoId][strtolower($fiat)];
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('CoinGecko fallback exception', [
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Map cryptocurrency code to CoinGecko ID
     * 
     * @param string $crypto Cryptocurrency code
     * @return string CoinGecko ID
     */
    private function mapCryptoToId($crypto)
    {
        $map = [
            'btc' => 'bitcoin',
            'eth' => 'ethereum',
            'ltc' => 'litecoin',
            'bch' => 'bitcoin-cash',
            'xrp' => 'ripple',
            'xlm' => 'stellar',
            'dash' => 'dash',
            'zec' => 'zcash',
            'usdt' => 'tether',
            'usdc' => 'usd-coin'
        ];
        
        return $map[strtolower($crypto)] ?? strtolower($crypto);
    }
}





