@extends($activeTemplate . 'layouts.master')
@section('content')
<div id="custom-bitgo-payment-ui">
    <div class="container payment-container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                @php
                    // Handle different data structures
                    if (isset($data->val)) {
                        $paymentData = is_object($data->val) ? $data->val : (is_array($data->val) ? (object)$data->val : null);
                    } else {
                        $paymentData = $data;
                    }

                    // Extract values with fallbacks
                    $address = $paymentData->address ?? null;
                    $currency = $paymentData->currency ?? 'BTC';
                    $transaction_id = $paymentData->transaction_id ?? '';
                    $fiat_amount = isset($paymentData->fiat_amount) && is_numeric($paymentData->fiat_amount) ? (float)$paymentData->fiat_amount : 0;
                    $fiat_currency = $paymentData->fiat_currency ?? 'USD';

                    // Calculate $amount based on paymentData values
                    $amount = 0; // Default to 0

                    if (isset($paymentData->exchange_rate) && is_numeric($paymentData->exchange_rate) && $paymentData->exchange_rate > 0 &&
                        $fiat_amount > 0) {
                        $amount = $fiat_amount / $paymentData->exchange_rate;
                    }

                    // Generate blockchain explorer URL based on currency
                    $explorer_urls = [
                        'BTC' => 'https://www.blockchain.com/btc/address/',
                        'ETH' => 'https://etherscan.io/address/',
                        'LTC' => 'https://blockchair.com/litecoin/address/',
                        'BCH' => 'https://blockchair.com/bitcoin-cash/address/',
                        'DASH' => 'https://blockchair.com/dash/address/',
                        'DOGE' => 'https://dogechain.info/address/',
                        'TRX' => 'https://tronscan.org/#/address/',
                        'BNB' => 'https://bscscan.com/address/',
                        'USDT' => 'https://usdt.tokenview.io/address/',
                    ];

                    $explorer_url = $explorer_urls[$currency] ?? 'https://www.blockchain.com/btc/address/';

                    // Set default expiry time if not provided
                    $expiry_minutes = $expiry_minutes ?? 30;
                @endphp
                
                <!-- Payment box with proper styling -->
                <div class="payment-box">
                    <div class="payment-header">
                        <h2 class="payment-title">{{ strtoupper($currency) }} Payment</h2>
                        <p class="text-muted">Order #{{ $transaction_id }}</p>
                    </div>
                    
                    <!-- Body -->
                    <div class="payment-amount">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="payment-amount-value">
                                @if(isset($paymentData->exchange_rate) && is_numeric($paymentData->exchange_rate) && $paymentData->exchange_rate > 0)
                                RATE: 1 {{ strtoupper($currency) }} = {{ number_format($paymentData->exchange_rate, 2) }} {{ $fiat_currency }}
                                @endif
                            </div>
                            <div class="payment-amount-value">
                                {{ number_format($fiat_amount, 2) }} {{ strtoupper($fiat_currency) }}
                            </div>
                        </div>
                        <div class="text-center mt-2" style="font-size: 0.9rem; color: #555;">
                            <span><strong>Debug Info:</strong></span><br>
                            <span>Address: {{ $address }}</span><br>
                            <span>Currency: {{ $currency }}</span><br>
                            <span>Fiat Amount: {{ number_format($fiat_amount, 2) }} {{ strtoupper($fiat_currency) }}</span><br>
                            <span>Fiat Currency: {{ strtoupper($fiat_currency) }}</span><br>
                            <span>Amount (Crypto): {{ number_format($amount, 8) }} {{ strtoupper($currency) }}</span>
                        </div>
                    </div>
                    
                    <div class="payment-instructions" style="background-color: #e9f7fe; color: #0c5460; padding: 1.5rem; border-radius: 0.8rem; margin-bottom: 1.5rem; position: relative; overflow: hidden; border: 1px solid rgba(12, 84, 96, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(12, 84, 96, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                        <!-- Gradient overlay for 3D effect -->
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                        <p class="payment-instructions-text">Send exactly <strong style="font-size: 1.7rem;"><span id="crypto-amount-repeat">{{ number_format($amount, 8) }}</span> <span id="crypto-currency-repeat">{{ strtoupper($currency) }}</span></strong> to this address:</p>
                    </div>
                    
<!-- Insert Debug code hear and display -->

                    <!-- Address box -->
                    <div class="payment-address" style="font-family: monospace; font-size: 1.32rem; word-break: break-all; margin-bottom: 1rem; padding: 0.8rem; background-color: #f8f9fa; border-radius: 0.5rem; box-shadow: inset 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.05), inset -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.7); border: 1px solid rgba(0, 0, 0, 0.05);">
                        <div class="d-flex justify-content-between align-items-center">
                            <span id="crypto-address">{{ $address }}</span>
                            <button class="btn btn-outline-secondary btn-copy" onclick="copyToClipboard()" title="Copy Address" style="padding: 0.5rem 1rem;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- New layout with QR code on left and status on right -->
                    <div class="row">
                        <div class="col-md-5">
                            <!-- QR Code -->
                            <div id="qrcode">
                                @php
                                    // Format the data for the QR code
                                    $qrData = "bitcoin:{$address}?amount={$amount}";
                                    
                                    // First try to use the Bacon QR Code library
                                    try {
                                        // Check if the Bacon QR Code library is available
                                        if (class_exists('BaconQrCode\Writer')) {
                                            // Create a renderer style
                                            $rendererStyle = new \BaconQrCode\Renderer\RendererStyle\RendererStyle(240);
                                            
                                            // Create an SVG image backend
                                            $imageBackEnd = new \BaconQrCode\Renderer\Image\SvgImageBackEnd();
                                            
                                            // Create an image renderer
                                            $renderer = new \BaconQrCode\Renderer\ImageRenderer($rendererStyle, $imageBackEnd);
                                            
                                            // Create a writer
                                            $writer = new \BaconQrCode\Writer($renderer);
                                            
                                            // Generate a QR code
                                            $qrCode = $writer->writeString($qrData);
                                        } else {
                                            // If the library is not available, throw an exception to trigger the fallback
                                            throw new \Exception('Bacon QR Code library not found');
                                        }
                                    } catch (\Exception $e) {
                                        // Log the error
                                        \Illuminate\Support\Facades\Log::error('QR Code error in Bitgo payment page', [
                                            'error' => $e->getMessage(),
                                            'address' => $address,
                                            'transaction_id' => $transaction_id ?? 'unknown'
                                        ]);
                                        
                                        // Fallback to the external API
                                        $qrCode = '<div style="text-align: center;">
                                            <img src="https://api.qrserver.com/v1/create-qr-code/?data=' . urlencode($qrData) . '&size=240x240&ecc=m" alt="QR Code" style="max-width: 240px;">
                                        </div>';
                                    }
                                @endphp
                                {!! $qrCode !!}
                                <p>Scan with your wallet app</p>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <!-- Payment status -->
                            <div class="payment-status status-waiting" style="padding: 1.2rem; border-radius: 0.8rem; margin-bottom: 1.5rem; background-color: #fff3cd; color: #856404; box-shadow: 0.2rem 0.2rem 0.5rem rgba(133, 100, 4, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5); position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.05);">
                                <!-- Gradient overlay for 3D effect -->
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%); pointer-events: none;"></div>

                                <div class="d-flex align-items-center" style="position: relative;">
                                    <i class="fas fa-clock me-3" style="font-size: 1.8rem;"></i>
                                    <div>
                                        <strong style="font-size: 1.44rem; display: block; margin-bottom: 0.5rem;">Waiting for payment</strong>
                                        <div style="font-size: 1.2rem;" id="status-message">Please send the exact amount to the address shown above</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Timer -->
                            <div class="text-center">
                                <div style="font-size: 1.2rem; color: #856404; font-weight: 500;">Payment expires in</div>
                                <div class="timer" id="countdown" style="font-size: 2.2rem;">30:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-3 mt-4">
                        <button class="btn btn-outline-secondary btn-lg py-3" id="checkStatus" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-sync-alt me-2"></i> Check Payment Status</span>
                        </button>
                        <a href="{{ $explorer_url }}{{ $address }}" target="_blank" class="btn btn-primary btn-lg py-3" id="btn-open-explorer" style="border-radius: 0.8rem; position: relative; overflow: hidden; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1), -0.2rem -0.2rem 0.5rem rgba(255, 255, 255, 0.5);">
                            <span style="position: relative; z-index: 2;"><i class="fas fa-search me-2"></i> Open in Blockchain Explorer</span>
                        </a>
                    </div>

                    <div class="payment-footer" style="border-top: 1px solid #e9ecef; padding-top: 1rem; margin-top: 1.5rem; font-size: 1.08rem; color: #6c757d;">
                        <div class="row">
                            <div class="col-md-6">
                                <small>Transaction ID: <span id="transaction-id">{{ $transaction_id }}</span></small>
                            </div>
                            <div class="col-md-6 text-end">
                                <small>Powered by FOC LABS LLC</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    /* Base styles with 20% larger fonts */
    html {
        font-size: 16.8px; /* 14px increased by 20% */
    }
    @media (min-width: 768px) {
        html {
            font-size: 19.2px; /* 16px increased by 20% */
        }
        .tooltip-inner {
            max-width: 350px;
        }
    }
    /* .payment-container defined below with ID scope */
    .box-shadow {
        box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
    }

    #custom-bitgo-payment-ui .payment-container {
        max-width: 980px;
        margin: 0 auto;
    }
    #custom-bitgo-payment-ui .payment-box {
        border: 1px solid #d1d9e6;
        border-radius: 1rem;
        padding: 1.8rem;
        margin-bottom: 2.4rem;
        background-color: #fff;
        box-shadow:
            0.5rem 0.5rem 1rem rgba(0, 0, 0, 0.1),
            -0.5rem -0.5rem 1rem rgba(255, 255, 255, 0.8),
            inset 0 0 0 rgba(255, 255, 255, 0.8),
            inset 0 0 0 rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
    }
    #custom-bitgo-payment-ui .payment-header {
        border-bottom: 1px solid #e9ecef !important;
        padding-bottom: 1.2rem !important;
        margin-bottom: 1.8rem !important;
    }
    #custom-bitgo-payment-ui .payment-title {
        font-size: 3.6rem !important; /* Doubled from 1.8rem */
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
        color: #000000 !important; /* Added black color */
    }
    #custom-bitgo-payment-ui .payment-amount-value {
        font-size: 1.44rem !important; /* 40% smaller than 2.4rem */
        font-weight: 700 !important;
        color: #000000 !important; /* Added black color */
    }
    #custom-bitgo-payment-ui .payment-instructions-text {
        font-size: 1.5rem !important;
        margin-bottom: 0rem !important; /* Changed from 1rem */
        position: relative !important; /* Kept from original inline style */
    }
</style>
@endpush

@push('script')
<script>
    // Copy to clipboard function
    function copyToClipboard() {
        var copyText = document.getElementById("crypto-address");
        
        // Create a temporary input element
        var tempInput = document.createElement("input");
        tempInput.value = copyText.textContent;
        document.body.appendChild(tempInput);
        
        // Select and copy the text
        tempInput.select();
        document.execCommand("copy");
        
        // Remove the temporary element
        document.body.removeChild(tempInput);
        
        // Alert the copied text
        alert("Address copied: " + copyText.textContent);
    }
    
    // Countdown timer
    $(document).ready(function() {
        var countdownElement = document.getElementById('countdown');
        var minutes = {{ $expiry_minutes }};
        var seconds = 0;
        
        var countdownInterval = setInterval(function() {
            if (seconds == 0) {
                if (minutes == 0) {
                    clearInterval(countdownInterval);
                    countdownElement.innerHTML = "00:00";
                    return;
                }
                minutes--;
                seconds = 59;
            } else {
                seconds--;
            }
            
            countdownElement.innerHTML = 
                (minutes < 10 ? '0' + minutes : minutes) + ':' + 
                (seconds < 10 ? '0' + seconds : seconds);
        }, 1000);
        
        // Check payment status button
        $('#checkStatus').on('click', function() {
            var $btn = $(this);
            $btn.prop('disabled', true);
            $btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Checking...');

            // Simulate checking payment status
            setTimeout(function() {
                $btn.prop('disabled', false);
                $btn.html('<i class="fas fa-sync-alt me-2"></i> Check Payment Status');
                
                // For demo purposes only
                alert('Payment status: Waiting for payment');
            }, 2000);
        });
    });
</script>
@endpush
