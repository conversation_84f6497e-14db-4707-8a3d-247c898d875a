<?php
// Fix General Settings for FocLabs
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\GeneralSetting;

echo "Checking General Settings...\n";

// Check if general settings exist
$general = GeneralSetting::first();

if (!$general) {
    echo "No general settings found. Creating default settings...\n";
    
    // Create default general settings
    $general = new GeneralSetting();
    $general->site_name = 'FocLabs';
    $general->cur_text = 'USD';
    $general->cur_sym = '$';
    $general->email_from = '<EMAIL>';
    $general->email_template = '<div>{{message}}</div>';
    $general->sms_template = '{{message}}';
    $general->sms_from = 'FocLabs';
    $general->base_color = '#3498db';
    $general->secondary_color = '#2ecc71';
    $general->mail_config = json_encode([
        'name' => 'php'
    ]);
    $general->sms_config = json_encode([
        'name' => 'nexmo'
    ]);
    $general->global_shortcodes = json_encode([
        'site_name' => 'FocLabs',
        'site_currency' => 'USD'
    ]);
    $general->kv = 1;
    $general->ev = 1;
    $general->en = 1;
    $general->sv = 1;
    $general->sn = 1;
    $general->force_ssl = 0;
    $general->maintenance_mode = 0;
    $general->secure_password = 0;
    $general->registration = 1;
    $general->agree = 1;
    $general->multi_language = 1;
    $general->active_template = 'basic';
    $general->system_info = 1;
    $general->last_cron = now();
    
    try {
        $general->save();
        echo "✅ Default general settings created successfully!\n";
        echo "Site Name: " . $general->site_name . "\n";
        echo "Maintenance Mode: " . ($general->maintenance_mode ? 'ON' : 'OFF') . "\n";
        echo "Registration: " . ($general->registration ? 'ENABLED' : 'DISABLED') . "\n";
        echo "Active Template: " . $general->active_template . "\n";
    } catch (Exception $e) {
        echo "❌ Error creating general settings: " . $e->getMessage() . "\n";
    }
} else {
    echo "✅ General settings already exist!\n";
    echo "Site Name: " . $general->site_name . "\n";
    echo "Maintenance Mode: " . ($general->maintenance_mode ? 'ON' : 'OFF') . "\n";
    echo "Registration: " . ($general->registration ? 'ENABLED' : 'DISABLED') . "\n";
    echo "Active Template: " . $general->active_template . "\n";
    
    // Ensure maintenance_mode is set
    if ($general->maintenance_mode === null) {
        echo "Fixing null maintenance_mode...\n";
        $general->maintenance_mode = 0;
        $general->save();
        echo "✅ Maintenance mode set to OFF\n";
    }
}

echo "\nClearing cache...\n";
try {
    \Illuminate\Support\Facades\Cache::forget('icolab_cache_GeneralSetting');
    echo "✅ Cache cleared successfully!\n";
} catch (Exception $e) {
    echo "⚠️ Cache clear warning: " . $e->getMessage() . "\n";
}

echo "\nDone! You can now test the application.\n";
?>
