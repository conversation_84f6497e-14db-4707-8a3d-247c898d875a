<?php
session_start();

// --- Login Logic ---
if (isset($_POST['password'])) {
    if ($_POST['password'] === '2211') {
        $_SESSION['authenticated'] = true;
        header("Location: clear.php");
        exit;
    } else {
        $error = "Incorrect password.";
    }
}

// --- AJAX Action to Clear Cache with Count Summary ---
if (isset($_GET['action']) && $_GET['action'] === 'clear') {
    if (!isset($_SESSION['authenticated']) || $_SESSION['authenticated'] !== true) {
        echo json_encode(["status" => "error", "message" => "Unauthorized access."]);
        exit;
    }

    // Get the Laravel root directory (assumes clear.php is in the public directory)
    $root = realpath(__DIR__ . '/../');

    // Count items for Route Cache (usually a single file)
    $routeCacheFile = $root . '/bootstrap/cache/routes.php';
    $routeCount = file_exists($routeCacheFile) ? 1 : 0;

    // Count items for Config Cache (usually a single file)
    $configCacheFile = $root . '/bootstrap/cache/config.php';
    $configCount = file_exists($configCacheFile) ? 1 : 0;

    // Count items for View Cache (compiled views stored in a directory)
    $viewCacheDir = $root . '/storage/framework/views';
    $viewCount = 0;
    if (is_dir($viewCacheDir)) {
        // Exclude '.' and '..'
        $files = array_diff(scandir($viewCacheDir), array('.', '..'));
        $viewCount = count($files);
    }

    // Execute the artisan commands
    shell_exec("php artisan route:clear");
    shell_exec("php artisan view:clear");
    shell_exec("php artisan config:clear");

    // Prepare summary messages with counts
    $results = [];
    $results['Route Cache']  = "Deleted {$routeCount} item" . ($routeCount !== 1 ? "s" : "");
    $results['View Cache']   = "Deleted {$viewCount} item" . ($viewCount !== 1 ? "s" : "");
    $results['Config Cache'] = "Deleted {$configCount} item" . ($configCount !== 1 ? "s" : "");

    echo json_encode(["status" => "success", "results" => $results]);
    exit;
}

// --- If Not Authenticated, Show the Login Form ---
if (!isset($_SESSION['authenticated']) || $_SESSION['authenticated'] !== true) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Clear Cache - Login</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f0f0f0; }
            .login-container {
                width: 300px;
                margin: 100px auto;
                background: #fff;
                padding: 20px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                border-radius: 8px;
            }
            input[type=password], input[type=submit] {
                width: 100%;
                padding: 10px;
                margin: 10px 0;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            .error { color: red; }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h2>Please Enter Password</h2>
            <?php if(isset($error)) { echo '<p class="error">'.$error.'</p>'; } ?>
            <form method="post" action="clear.php">
                <input type="password" name="password" placeholder="Password" required>
                <input type="submit" value="Login">
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Laravel Cache Manager</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f7f7f7; }
        .container {
            width: 500px;
            margin: 50px auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 { text-align: center; }
        .progress-container {
            width: 100%;
            background: #e0e0e0;
            border-radius: 5px;
            margin: 20px 0;
            height: 20px;
        }
        .progress-bar {
            width: 0%;
            height: 100%;
            background: #4caf50;
            border-radius: 5px;
            text-align: center;
            color: white;
            line-height: 20px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Laravel Cache Manager</h1>
        <p>This tool will clear the following caches:</p>
        <ol>
            <li>Route Cache</li>
            <li>View Cache</li>
            <li>Config Cache</li>
        </ol>
        <div class="progress-container">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>
        <div>
            <button class="button" id="clearCacheButton">Clear Cache</button>
            <button class="button" id="closeButton">Close</button>
        </div>
        <div id="result" style="margin-top:20px;"></div>
    </div>
    <script>
    document.getElementById('clearCacheButton').addEventListener('click', function() {
        var progressBar = document.getElementById('progressBar');
        var resultDiv = document.getElementById('result');
        resultDiv.innerHTML = "";
        
        // Reset and animate the progress bar
        progressBar.style.width = "0%";
        progressBar.textContent = "0%";
        var width = 0;
        var interval = setInterval(function() {
            width += 10;
            if (width > 100) width = 100;
            progressBar.style.width = width + '%';
            progressBar.textContent = width + '%';
            if (width >= 100) {
                clearInterval(interval);
            }
        }, 200);
        
        // Send AJAX request to clear the cache
        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'clear.php?action=clear', true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState == 4 && xhr.status == 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if(response.status === "success"){
                        var output = "<h3>Cache Cleared Successfully:</h3><ol>";
                        var i = 1;
                        for(var key in response.results) {
                            output += "<li>" + key + ": " + response.results[key] + "</li>";
                            i++;
                        }
                        output += "</ol>";
                        resultDiv.innerHTML = output;
                    } else {
                        resultDiv.innerHTML = "<p style='color:red;'>Error: " + response.message + "</p>";
                    }
                } catch (e) {
                    resultDiv.innerHTML = "<p style='color:red;'>An error occurred while processing the response.</p>";
                }
            }
        };
        xhr.send();
    });

    document.getElementById('closeButton').addEventListener('click', function() {
        // Attempt to close the window; if not allowed, redirect to a blank page.
        window.open('', '_self', '');
        window.close();
        setTimeout(function(){
            window.location.href = 'about:blank';
        }, 100);
    });
    </script>
</body>
</html>