@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="container">
        <div class="row justify-content-center mt-3">
            <div class="card custom--card">
                <div class="card-body">
                    @if (auth()->user()->referrer)
                        <div class="card-header mb-2">
                            <h4 class="mb-2">@lang('You are referred by') {{ auth()->user()->referrer->fullname }}</h4>
                        </div>
                    @endif

                    <div class="col-md-12 mb-4">
                        @include($activeTemplate . 'partials.referral')
                    </div>

                    @if ($user->allReferrals->count() > 0 && $maxLevel > 0)
                        <div class="treeview-container">
                            <ul class="treeview">
                                <li class="items-expanded"> {{ $user->fullname }} ( {{ $user->username }} )
                                    @include($activeTemplate . 'partials.under_tree', [
                                        'user' => $user,
                                        'layer' => 0,
                                        'isFirst' => true,
                                    ])
                                </li>
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style-lib')
    <link type="text/css" href="{{ asset('assets/global/css/jquery.treeView.css') }}" rel="stylesheet">
@endpush

@push('script')
    <script src="{{ asset('assets/global/js/jquery.treeView.js') }}"></script>
    <script>
        (function($) {
            "use strict"
            $('.treeview').treeView();
            $('.copyBoard').click(function() {
                var copyText = document.getElementsByClassName("referralURL");
                copyText = copyText[0];
                copyText.select();
                copyText.setSelectionRange(0, 99999);

                /*For mobile devices*/
                document.execCommand("copy");
                copyText.blur();
                this.classList.add('copied');
                setTimeout(() => this.classList.remove('copied'), 1500);
            });
        })(jQuery);
    </script>
@endpush
