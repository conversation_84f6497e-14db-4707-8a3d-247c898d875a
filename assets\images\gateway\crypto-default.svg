<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="512" height="512" viewBox="0 0 512 512">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3d5a80;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3d5a80;stop-opacity:1" />
    </linearGradient>
  </defs>
  <g>
    <circle cx="256" cy="256" r="256" fill="url(#grad)"/>
    <path d="M256,128 C185.307,128 128,185.307 128,256 C128,326.693 185.307,384 256,384 C326.693,384 384,326.693 384,256 C384,185.307 326.693,128 256,128 Z M256,368 C194.256,368 144,317.744 144,256 C144,194.256 194.256,144 256,144 C317.744,144 368,194.256 368,256 C368,317.744 317.744,368 256,368 Z" fill="white"/>
    <path d="M256,176 C211.84,176 176,211.84 176,256 C176,300.16 211.84,336 256,336 C300.16,336 336,300.16 336,256 C336,211.84 300.16,176 256,176 Z M256,320 C220.672,320 192,291.328 192,256 C192,220.672 220.672,192 256,192 C291.328,192 320,220.672 320,256 C320,291.328 291.328,320 256,320 Z" fill="white"/>
    <circle cx="256" cy="256" r="48" fill="white"/>
  </g>
</svg>
