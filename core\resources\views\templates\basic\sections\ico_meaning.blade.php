@php
    $icoContent = getContent('ico_meaning.content', true);
@endphp

<!-- about section start -->
<section class="pt-100 pb-100 scroll-section">
    <div class="container">
    <div class="row align-items-center justify-content-between">
        <div class="col-lg-6 wow fadeInLeft" data-wow-duration="0.5" data-wow-delay="0.3s">
        <h2 class="section-title">{{ __(@$icoContent->data_values->heading) }}</h2>
        <p class="section-subtitle mt-3">
            {{ __(@$icoContent->data_values->description) }}
        </p>
        <div class="btn--group mt-4">
            <a href="#phase" class="btn btn--base scroll-section">{{ __(@$icoContent->data_values->button_name) }}</a>
            <a href="{{ @$icoContent->data_values->video_link }}" data-rel="lightcase:myCollection" class="video-btn video-btn--sm d-lg-none d-d-inline-flex"><i class="las la-play"></i></a>
        </div>
        </div>
        <div class="col-lg-5 d-lg-block d-none wow fadeInRight" data-wow-duration="0.5" data-wow-delay="0.5s">
            <div class="about-thumb">
                <a href="{{ @$icoContent->data_values->video_link }}" data-rel="lightcase:myCollection" class="video-btn play-icon"><i class="las la-play"></i></a>
                <img src="{{ frontendImage( 'ico_meaning', @$icoContent->data_values->background_image, '530x585') }}" alt="image">
            </div>
        </div>
    </div>
    </div>
</section>
<!-- about section end -->
@push('style')
<style>
    .about-thumb:hover a{
        color: #fff;
    }
</style>
@endpush




