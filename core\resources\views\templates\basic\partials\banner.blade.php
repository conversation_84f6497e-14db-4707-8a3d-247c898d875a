@php
    $bannerContent = getContent('banner.content', true);
@endphp
<section class="hero bg_img scroll-section"
    style="background-image: url({{ frontendImage('banner', @$bannerContent->data_values->image, '1920x1080') }});"
    data-paroller-factor="0.3">
    <div class="container">
        <div class="row justify-content-between align-items-center">
            <div class="col-xxl-6 col-lg-6 text-lg-start text-center">
                <h2 class="hero__title wow fadeInUp" data-wow-duration="0.5" data-wow-delay="0.3s">
                    {{ __(@$bannerContent->data_values->heading) }} </h2>
                <p class="mt-3 wow fadeInUp" data-wow-duration="0.5" data-wow-delay="0.5s">
                    {{ __(@$bannerContent->data_values->subheading) }}</p>
                <div class="mt-lg-5 mt-3 wow fadeInUp" data-wow-duration="0.5" data-wow-delay="0.3s">
                    {{-- Commenting out the Explore Platform button
                    <a href="{{ @$bannerContent->data_values->button_link }}" class="btn btn--base magnetic-effect">
                        {{ __(@$bannerContent->data_values->button_name) }}
                    </a>
                    --}}
                </div>
            </div>
            <div class="col-xxl-5 col-lg-6 mt-lg-0 mt-5">
                <div class="count-wrapper glass--bg rounded-2 text-center">
                    <h2 class="title">{{ __($phaseType) }} {{ __(strtoupper(@$phase->stage)) }} @lang('PHASE') </h2>
                    <p class="mt-2">{{ __(@$bannerContent->data_values->phase_content) }}</p>
                    <div id="countdown" class="mt-5" data-date=" @if ($phaseType == 'RUNNING') {{ showDateTime(@$phase->end_date,'m/d/Y H:i:s') }} @elseif($phaseType == 'UPCOMING'){{ showDateTime(@$phase->start_date,'m/d/Y H:i:s') }} @else {{ Carbon\Carbon::now()->subDays(1)->format('m/d/Y H:i:s') }} @endif ">
                        <ul class="date-unit-list d-flex flex-wrap justify-content-between">
                            <li class="single-unit"><span id="days"></span>@lang('Days')</li>
                            <li class="single-unit"><span id="hours"></span>@lang('Hours')</li>
                            <li class="single-unit"><span id="minutes"></span>@lang('Min')</li>
                            <li class="single-unit"><span id="seconds"></span>@lang('Sec')</li>
                        </ul>
                    </div>
                    <h4 class="mt-3">@lang('Current Price') : {{ showAmount(@$phase->price) }}</h4>
                    <a href="{{ route('user.coin.buy') }}" class="btn btn--base mt-4"> <i class="las la-coins"></i>
                        @lang('Buy Token Now')</a>
                </div>
            </div>
        </div>
    </div>
</section>
@push('script')
    <script>
        (function($) {
            "use strict"
            // variable for time units
            const second = 1000,
                  minute = second * 60,
                  hour   = minute * 60,
                  day    = hour * 24;

            // get attribute date data from HTML
            let dateData  = document.querySelector('#countdown').getAttribute('data-date');

            let finalDay  = dateData,

                countDown = new Date(finalDay).getTime(),
                       x  = setInterval(function() {

                    let now      = new Date().getTime(),
                        distance = countDown - now;

                    let daysVar    = document.getElementById("days");
                    let hoursVar   = document.getElementById("hours");
                    let minutesVar = document.getElementById("minutes");
                    let secondsVar = document.getElementById("seconds");

                    daysVar.innerText    = Math.floor(distance / (day)),
                    hoursVar.innerText   = Math.floor((distance % (day)) / (hour)),
                    minutesVar.innerText = Math.floor((distance % (hour)) / (minute)),
                    secondsVar.innerText = Math.floor((distance % (minute)) / second);

                    //do something later when date is reached
                    if (distance < 0) {
                        daysVar.innerText    = "0";
                        hoursVar.innerText   = "0";
                        minutesVar.innerText = "0";
                        secondsVar.innerText = "0";
                        clearInterval(x);
                    }
                    seconds
                }, 0)
        })(jQuery);
    </script>
@endpush

