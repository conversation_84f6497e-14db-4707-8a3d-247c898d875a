{"dashboard": {"keyword": ["Dashboard", "Home", "Panel", "Admin", "Control center", "Overview", "Main hub", "Management hub", "Administrative hub", "Central hub", "Command center", "Administrator portal", "Centralized interface", "Admin console", "Management dashboard", "Main screen", "Administrative dashboard", "Command dashboard", "Main control panel"], "title": "Dashboard", "icon": "las la-home", "route_name": "admin.dashboard", "menu_active": "admin.dashboard"}, "manage_ico": {"keyword": ["ico", "manage ico", "Ico management", "ico list", "ico add"], "title": "Manage Ico", "icon": "las la-coins", "route_name": "admin.ico.index", "menu_active": "admin.ico.*"}, "white_paper": {"keyword": ["white paper", "manage white paper", "white paper management", "white paper list", "white paper add"], "title": "White Paper", "icon": "las la-sticky-note", "route_name": "admin.white.paper.index", "menu_active": "admin.white.paper.*"}, "manage_users": {"title": "Manage Users", "icon": "las la-users", "counters": ["bannedUsersCount", "emailUnverifiedUsersCount", "mobileUnverifiedUsersCount", "kycUnverifiedUsersCount", "kycPendingUsersCount"], "menu_active": "admin.users*", "submenu": [{"keyword": ["active users", "Manage Users", "User management", "User control", "User status", "User activity", "User analytics"], "title": "Active Users", "route_name": "admin.users.active", "menu_active": "admin.users.active"}, {"keyword": ["banned users", "Manage Users", "User management", "Account bans", "User activity"], "title": "Banned Users", "route_name": "admin.users.banned", "menu_active": "admin.users.banned", "counter": "bannedUsersCount"}, {"keyword": ["email unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Email Unverified", "route_name": "admin.users.email.unverified", "menu_active": "admin.users.email.unverified", "counter": "emailUnverifiedUsersCount"}, {"keyword": ["mobile unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "Mobile Unverified", "route_name": "admin.users.mobile.unverified", "menu_active": "admin.users.mobile.unverified", "counter": "mobileUnverifiedUsersCount"}, {"keyword": ["kyc unverified users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Unverified", "route_name": "admin.users.kyc.unverified", "menu_active": "admin.users.kyc.unverified", "counter": "kycUnverifiedUsersCount"}, {"keyword": ["kyc pending users", "Manage Users", "User verification", "User authentication", "User management"], "title": "KYC Pending", "route_name": "admin.users.kyc.pending", "menu_active": "admin.users.kyc.pending", "counter": "kycPendingUsersCount"}, {"keyword": ["with balance users", "Manage Users", "User management", "User activity", "Account management"], "title": "With Balance", "route_name": "admin.users.with.balance", "menu_active": "admin.users.with.balance"}, {"keyword": ["all users users", "Manage Users", "User management", "User control", "User activity", "User analytics"], "title": "All Users", "route_name": "admin.users.all", "menu_active": "admin.users.all"}, {"keyword": ["send notification users", "Manage Users", "User notifications", "User management", "User activity"], "title": "Send Notification", "route_name": "admin.users.notification.all", "menu_active": "admin.users.notification.all"}]}, "deposits": {"title": "Deposits", "icon": "las la-file-invoice-dollar", "counters": ["pendingDepositsCount"], "menu_active": "admin.deposit*", "submenu": [{"keyword": ["Pending Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit status", "Deposit activity"], "title": "Pending Deposits", "route_name": "admin.deposit.pending", "menu_active": "admin.deposit.pending", "counter": "pendingDepositsCount", "params": {"user_id": ""}}, {"keyword": ["Approved Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Approved Deposits", "route_name": "admin.deposit.approved", "menu_active": "admin.deposit.approved", "params": {"user_id": ""}}, {"keyword": ["Successful Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Successful Deposits", "route_name": "admin.deposit.successful", "menu_active": "admin.deposit.successful", "params": {"user_id": ""}}, {"keyword": ["Rejected Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Rejected Deposits", "route_name": "admin.deposit.rejected", "menu_active": "admin.deposit.rejected", "params": {"user_id": ""}}, {"keyword": ["Initiated Deposits", "Deposits", "Deposit management", "Deposit activity"], "title": "Initiated Deposits", "route_name": "admin.deposit.initiated", "menu_active": "admin.deposit.initiated", "params": {"user_id": ""}}, {"keyword": ["All Deposits", "Deposits", "Deposit management", "Deposit control", "Deposit activity"], "title": "All Deposits", "route_name": "admin.deposit.list", "menu_active": "admin.deposit.list", "params": {"user_id": ""}}]}, "withdrawals": {"title": "Blockchain Transfer", "icon": "la la-bank", "counters": ["pendingWithdrawCount"], "menu_active": "admin.withdraw.data*", "submenu": [{"keyword": ["Pending Blockchain Transfer", "Blockchain Transfer", "Transfer management", "Transfer control", "Transfer status", "Transfer activity"], "title": "Pending Transfers", "route_name": "admin.withdraw.data.pending", "menu_active": "admin.withdraw.data.pending", "counter": "pendingWithdrawCount", "params": {"user_id": ""}}, {"keyword": ["Approved Blockchain Transfer", "Blockchain Transfer", "Transfer management", "Transfer activity"], "title": "Approved Transfers", "route_name": "admin.withdraw.data.approved", "menu_active": "admin.withdraw.data.approved", "params": {"user_id": ""}}, {"keyword": ["Rejected Blockchain Transfer", "Blockchain Transfer", "Transfer management", "Transfer activity"], "title": "Rejected Transfers", "route_name": "admin.withdraw.data.rejected", "menu_active": "admin.withdraw.data.rejected", "params": {"user_id": ""}}, {"keyword": ["All Blockchain Transfer", "Blockchain Transfer", "Transfer management", "Transfer control", "Transfer activity"], "title": "All Transfers", "route_name": "admin.withdraw.data.all", "menu_active": "admin.withdraw.data.all", "params": {"user_id": ""}}]}, "support_ticket": {"title": "Support Ticket", "icon": "la la-ticket", "counters": ["pendingTicketCount"], "menu_active": "admin.ticket*", "submenu": [{"keyword": ["Pending Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket status", "Ticket activity"], "title": "Pending Ticket", "route_name": "admin.ticket.pending", "menu_active": "admin.ticket.pending", "counter": "pendingTicketCount"}, {"keyword": ["Closed Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Closed Ticket", "route_name": "admin.ticket.closed", "menu_active": "admin.ticket.closed"}, {"keyword": ["Answered Ticket", "Support Ticket", "Ticket management", "Ticket activity"], "title": "Answered Ticket", "route_name": "admin.ticket.answered", "menu_active": "admin.ticket.answered"}, {"keyword": ["All Ticket", "Support Ticket", "Ticket management", "Ticket control", "Ticket activity"], "title": "All Ticket", "route_name": "admin.ticket.index", "menu_active": "admin.ticket.index"}]}, "reports": {"title": "Report", "icon": "la la-list", "menu_active": "admin.report*", "submenu": [{"keyword": ["Coin History", "Report", "Coin report", "Coin log", "Coin activity"], "title": "Coin History", "route_name": "admin.report.coins", "menu_active": ["admin.report.coins", "admin.report.coins.search"], "params": {"user_id": ""}}, {"keyword": ["Auction History", "Auction", "Auction report", "Auction log", "Auction activity"], "title": "Auction History", "route_name": "admin.report.auctions", "menu_active": ["admin.report.auctions", "admin.report.auctions.search"], "params": {"user_id": ""}}, {"keyword": ["Transaction Log", "Report", "Transaction report", "Transaction history", "Transaction activity", "balance sheet", "balance log", "balance history"], "title": "Transaction History", "route_name": "admin.report.transaction", "menu_active": ["admin.report.transaction", "admin.report.transaction.search"], "params": {"user_id": ""}}, {"keyword": ["Login History", "Report", "Login report", "Login history", "Login activity"], "title": "Login History", "route_name": "admin.report.login.history", "menu_active": ["admin.report.login.history", "admin.report.login.ipHistory"]}, {"keyword": ["Notification History", "Report", "Notification report", "Notification history", "Notification activity", "email log", "email history", "sms log", "sms history", "push notification log", "push notification history"], "title": "Notification History", "route_name": "admin.report.notification.history", "menu_active": "admin.report.notification.history"}]}, "subscriber": {"keyword": ["subscriber", "subscribers", "Subscription management", "Subscriber list", "Subscriber activity"], "title": "Subscribers", "icon": "las la-thumbs-up", "route_name": "admin.subscriber.index", "menu_active": "admin.subscriber.*"}, "system_setting": {"keyword": ["System Setting", "setting", "System configuration", "System preferences", "Configuration management", "System setup"], "title": "System Setting", "icon": "las la-life-ring", "route_name": "admin.setting.system", "menu_active": ["admin.setting.system", "admin.setting.general", "admin.setting.socialite.credentials", "admin.setting.system.configuration", "admin.setting.logo.icon", "admin.extensions.index", "admin.language.manage", "admin.language.key", "admin.seo", "admin.kyc.setting", "admin.frontend.templates", "admin.frontend.manage.*", "admin.maintenance.mode", "admin.setting.cookie", "admin.setting.custom.css", "admin.setting.sitemap", "admin.setting.robot", "admin.setting.notification.global.email", "admin.setting.notification.global.sms", "admin.setting.notification.global.push", "admin.setting.notification.email", "admin.setting.notification.sms", "admin.setting.notification.push", "admin.setting.notification.templates", "admin.setting.notification.template.edit", "admin.frontend.index", "admin.frontend.sections*", "admin.gateway*", "admin.withdraw.method*", "admin.setting.app.purchase"]}, "extra": {"title": "Utilities", "icon": "la la-server", "menu_active": "admin.system*", "counters": ["updateAvailable"], "submenu": [{"keyword": ["Application", "System", "Application management", "Application settings", "System information", "version", "laravel", "timezone"], "title": "Application", "route_name": "admin.system.info", "menu_active": "admin.system.info"}, {"keyword": ["Server", "System", "Server management", "Server settings", "System information", "version", "php version", "software", "ip address", "server ip address", "server port", "http host"], "title": "Server", "route_name": "admin.system.server.info", "menu_active": "admin.system.server.info"}, {"keyword": ["<PERSON><PERSON>", "System", "Cache management", "Cache optimization", "System performance", "clear cache"], "title": "<PERSON><PERSON>", "route_name": "admin.system.optimize", "menu_active": "admin.system.optimize"}]}, "report_and_request": {"keyword": ["Report & Request", "Report and Request", "Reports and Requests", "Reporting and Requests", "Report management", "Request management", "feature request", "bug report"], "title": "Report & Request", "icon": "las la-bug", "route_name": "admin.request.report", "menu_active": "admin.request.report"}}