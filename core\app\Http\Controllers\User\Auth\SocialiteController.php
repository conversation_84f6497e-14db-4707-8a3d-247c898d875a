<?php

namespace App\Http\Controllers\User\Auth;

use App\Http\Controllers\Controller;
use App\Lib\SocialLogin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SocialiteController extends Controller
{

    public function socialLogin($provider)
    {
        try {
            // Special handling for X/Twitter
            if ($provider === 'x') {
                $credentials = gs('socialite_credentials');
                if (!isset($credentials->x) || empty($credentials->x->client_id) || empty($credentials->x->client_secret)) {
                    throw new Exception('X.com login is not properly configured');
                }
            }

            $socialLogin = new SocialLogin($provider);
            return $socialLogin->redirectDriver();
        } catch (\Exception $e) {
            $notify[] = ['error', $e->getMessage()];
            return to_route('user.login')->withNotify($notify);
        }
    }


    public function callback(Request $request, $provider)
    {
        Log::info("Social login callback received", [
            'provider' => $provider,
            'all_parameters' => $request->all(),
        ]);

        // Special handling for X/Twitter errors
        if ($provider === 'x' && $request->has('error')) {
            Log::error("X.com login error", [
                'error' => $request->get('error'),
                'error_description' => $request->get('error_description')
            ]);
            
            $notify[] = ['error', 'Authentication failed: ' . $request->get('error_description', 'Unknown error')];
            return to_route('user.login')->withNotify($notify);
        }

        try {
            $socialLogin = new SocialLogin($provider);
            return $socialLogin->login();
        } catch (\Exception $e) {
            Log::error("Social login exception", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $notify[] = ['error', 'Authentication failed. Please try again.'];
            return to_route('user.login')->withNotify($notify);
        }
    }
}





