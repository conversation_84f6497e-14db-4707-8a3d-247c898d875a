@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="container" style="margin-top: -75px;">
        <div class="row justify-content-center">
            <div class="col-lg-9">
                <form action="{{ route('user.coin.purchase') }}" method="post" class="purchase-form">
                    @csrf
                    <input type="hidden" name="currency">
                    <div class="gateway-card custom--card">
                        <div class="row justify-content-center gy-sm-3 gy-2 pt-2" style="display: flex; align-items: stretch;">
                            <div class="col-lg-6" style="display: flex; flex-direction: column; min-height: 550px;">
                                <div class="payment-system-list is-scrollable gateway-option-list" style="flex-grow: 1;">
                                    @foreach ($gateways as $data)
                                        <label for="{{ titleToKey($data->name) }}"
                                            class="payment-item @if ($loop->index > 4) d-none @endif gateway-option">
                                            <div class="payment-item__info">
                                                <span class="payment-item__check"></span>
                                                <span class="payment-item__name">{{ __($data->name) }}</span>
                                            </div>
                                            <div class="payment-item__thumb">
                                                <img class="payment-item__thumb-img"
                                                    src="{{ $data->icon_url ? $data->icon_url : getImage(getFilePath('gateway') . '/' . $data->image) }}"
                                                    alt="@lang('payment-thumb')">
                                            </div>
                                            <input class="payment-item__radio gateway-input"
                                                id="{{ titleToKey($data->name) }}" hidden
                                                data-gateway='@json($data)' type="radio" name="gateway"
                                                value="{{ $data->method_code }}"
                                                @if (old('gateway')) @checked(old('gateway') == $data->method_code) @else @checked($loop->first) @endif
                                                data-min-amount="{{ showAmount($data->min_amount) }}"
                                                data-max-amount="{{ showAmount($data->max_amount) }}">
                                        </label>
                                    @endforeach
                                    @if ($gateways->count() > 4)
                                        <button type="button" class="payment-item__btn more-gateway-option">
                                            <p class="payment-item__btn-text">@lang('Show All Payment Options')</p>
                                            <span class="payment-item__btn__icon"><i class="fas fa-chevron-down"></i></span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6" style="display: flex; flex-direction: column; min-height: 550px;">
                                <div class="payment-system-list p-3" style="flex-grow: 1;">
                                    <div class="purchase-info">
                                        <div class="purchase-info__title">
                                            <p class="text mb-0">@lang('Amount in USD')</p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <div class="purchase-info__input-group input-group">
                                                <span class="purchase-info__input-group-text">$</span>
                                                <input type="text" class="form-control form--control amount"
                                                    name="amount" placeholder="@lang('00.00')"
                                                    value="{{ old('amount') }}" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="purchase-info">
                                        <div class="purchase-info__title">
                                            <p class="text has-icon"> @lang('Exchange Limits in USD')</p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <p class="text"><span class="gateway-limit">@lang('0.00')</span></p>
                                        </div>
                                    </div>
                                    <div class="purchase-info">
                                        <div class="purchase-info__title">
                                            <p class="text has-icon">@lang('Processing Charge')
                                                <span data-bs-toggle="tooltip" title="@lang('Processing charge for payment gateways')"
                                                    class="proccessing-fee-info"><i class="las la-info-circle"></i>
                                                </span>
                                            </p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <p class="text">$<span class="processing-fee">@lang('0.00')</span>
                                            </p>
                                        </div>
                                    </div>

                                    <div class="purchase-info total-amount py-2 mt-2">
                                        <div class="purchase-info__title">
                                            <p class="text">@lang('Total in USD')</p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <div class="purchase-info__input-group input-group">
                                                <span class="purchase-info__input-group-text">$</span>
                                                <span class="form-control form--control text-end final-amount" style="min-height: 38px;">@lang('0.00')</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="purchase-info gateway-conversion d-none total-amount pt-2">
                                        <div class="purchase-info__title">
                                            <p class="text">@lang('Conversion')
                                            </p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <p class="text"></p>
                                        </div>
                                    </div>
                                    <div class="purchase-info conversion-currency d-none total-amount pt-2">
                                        <div class="purchase-info__title">
                                            <p class="text">
                                                @lang('In') <span class="gateway-currency"></span>
                                            </p>
                                        </div>
                                        <div class="purchase-info__input">
                                            <p class="text">
                                                <span class="in-currency"></span>
                                            </p>

                                        </div>
                                    </div>

                                    <!-- Important Disclaimer -->
                                    <div class="purchase-disclaimer mb-3" style="padding: 1rem; background-color: #fff3cd; border-radius: 0.5rem; border: 1px solid #ffeeba; margin-top: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="display: flex; align-items: flex-start;">
                                            <i class="fas fa-exclamation-triangle me-3 mt-1" style="color: #856404; font-size: 1.1rem;"></i>
                                            <div>
                                                <h6 style="font-weight: 700; color: #856404; margin-bottom: 0.4rem; font-size: 1rem;">Important Notice</h6>
                                                <p style="font-size: 0.90rem; color: #856404; margin-bottom: 0.4rem; line-height: 1.3;">
                                                    Payments are <strong>non-refundable</strong> and cannot be converted back to crypto. This payment will be used to acquire FOC tokens.
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Acknowledgment Checkbox -->
                                        <div class="form-check mt-3" style="padding-left: 1.5rem; border-top: 1px solid rgba(133, 100, 4, 0.2); padding-top: 0.60rem; margin-left: 0.4rem; margin-right: 0.5rem;">
                                            <input class="form-check-input custom-checkbox" type="checkbox" id="acknowledgeTerms" style="cursor: pointer; width: 1.25rem; height: 1.25rem; border: 2px solid #856404;">
                                            <label class="form-check-label" for="acknowledgeTerms" style="font-size: 0.95rem; color: #856404; cursor: pointer; font-weight: 500; padding-left: 0.5rem; line-height: 1.3;">
                                                I understand and agree that my payment is non-refundable and can only be used to acquire FOC.
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn--base w-100 mt-4 confirm-purchase-btn" disabled>
                                        <span>@lang('Acquire FOC Now')</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .purchase-info__input-group .form--control {
            background-color: transparent !important;
        }

        /* Style for the total amount field */
        .final-amount.form--control {
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;
            padding-left: 0 !important;
            padding-right: 0.5rem !important;
            height: auto !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-end !important;
        }

        /* Make the dollar sign in Total row match the Amount row */
        .purchase-info__input-group-text {
            background-color: #e9ecef !important;
            border: 1px solid #ced4da !important;
            border-radius: 0.25rem 0 0 0.25rem !important;
            padding: 0.375rem 0.75rem !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
            color: #495057 !important;
            text-align: center !important;
            white-space: nowrap !important;
            display: flex !important;
            align-items: center !important;
        }

        /* Make font sizes consistent between labels and values */
        .purchase-info__title .text,
        .purchase-info__input .text,
        .purchase-info__input .gateway-limit,
        .purchase-info__input .processing-fee,
        .purchase-info__input .final-amount,
        .purchase-info__input .in-currency,
        .purchase-info__input .rate,
        .purchase-info__input .method_currency {
            font-size: 1rem !important;
        }

        /* Make Total slightly larger for emphasis */
        .total-amount .purchase-info__title .text,
        .total-amount .purchase-info__input .text,
        .total-amount .purchase-info__input .final-amount {
            font-size: 1.1rem !important;
            font-weight: 500;
        }

        /* Ensure the dollar sign in Total row has the same size as Amount row */
        .total-amount .purchase-info__input-group-text {
            font-size: 1rem !important;
            font-weight: 400;
        }

        /* Improve vertical alignment and text alignment */
        .purchase-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .purchase-info__title,
        .purchase-info__input {
            display: flex;
            align-items: center;
        }

        .purchase-info__title {
            text-align: left;
        }

        .purchase-info__input {
            text-align: right;
        }

        .purchase-info__title .text,
        .purchase-info__input .text {
            margin-bottom: 0;
        }

        /* Ensure gateway limit stays on one line */
        .gateway-limit {
            white-space: nowrap;
            display: inline-block;
            text-align: right;
        }

        /* Style the Confirm Purchase button */
        .confirm-purchase-btn {
            color: white !important;
            font-weight: 800 !important;
            font-size: 1.6rem !important;
            padding: 1rem 1.5rem !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            text-align: center !important;
            line-height: 1 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            height: auto !important;
            border: 2px solid transparent !important;
            transition: background-color 0.3s, transform 0.3s, border-color 0.3s !important;
        }

        .confirm-purchase-btn span {
            display: inline-block;
            line-height: 1;
            padding: 0;
            margin: 0;
        }

        .confirm-purchase-btn:hover:not(:disabled) {
            transform: scale(1.02);
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
        }

        .confirm-purchase-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #6c757d !important;
        }

        /* Custom styling for the disclaimer checkbox */
        .custom-checkbox {
            position: relative;
            appearance: none;
            -webkit-appearance: none;
            background-color: #fff;
            border: 2px solid #856404 !important;
            border-radius: 0.25rem;
            cursor: pointer;
            width: 1.25rem !important;
            height: 1.25rem !important;
            margin-top: 0.15rem !important;
        }

        .custom-checkbox:checked {
            background-color: #856404 !important;
            border-color: #856404 !important;
        }

        .custom-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 0.85rem;
            font-weight: bold;
        }

        .custom-checkbox:focus {
            box-shadow: 0 0 0 0.2rem rgba(133, 100, 4, 0.25) !important;
            outline: none;
        }

        /* Improve the disclaimer box appearance */
        .purchase-disclaimer {
            transition: all 0.3s ease;
        }

        .purchase-disclaimer:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }

        /* Fix for payment method images */
        .payment-item__thumb-img {
            max-width: 100%;
            height: auto;
            max-height: 30px;
            object-fit: contain;
        }

        /* Style for payment method selection */
        .payment-item {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            transition: all 0.3s ease;
            padding: 10px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.05);
        }

        .payment-item:hover {
            border-color: var(--base);
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Fix payment options list height to match payment frame */
        .payment-system-list.gateway-option-list {
            height: 100%;
            min-height: 550px;
            overflow-y: auto;
            padding: 0.75rem;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.05);
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 0;
            position: relative;
        }

        .gateway-option-list .payment-item__btn {
            margin-top: auto;
            margin-bottom: 0;
        }

        .row.justify-content-center > .col-lg-6 {
            display: flex;
            flex-direction: column;
        }

        .payment-system-list.gateway-option-list::after {
            content: "";
            display: block;
            height: 10px;
        }

        .more-gateway-option {
            padding: 10px;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
            text-align: center;
            cursor: pointer;
        }

        .more-gateway-option:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--base);
        }
    </style>
@endpush

@push('script')
    <script>
        "use strict";
        (function($) {

            var amount = parseFloat($('.amount').val() || 0);
            var gateway, minAmount, maxAmount;

            $('.amount').on('input', function(e) {
                amount = parseFloat($(this).val());
                if (!amount) {
                    amount = 0;
                }
                calculation();
            });

            $('.gateway-input').on('change', function(e) {
                gatewayChange();
            });

            function gatewayChange() {
                let gatewayElement = $('.gateway-input:checked');
                let methodCode = gatewayElement.val();

                gateway = gatewayElement.data('gateway');
                minAmount = gatewayElement.data('min-amount');
                maxAmount = gatewayElement.data('max-amount');

                let processingFeeInfo =
                    `${parseFloat(gateway.percent_charge).toFixed(2)}% with ${parseFloat(gateway.fixed_charge).toFixed(2)} {{ __(gs('cur_text')) }} charge for payment gateway processing fees`
                $(".proccessing-fee-info").attr("data-bs-original-title", processingFeeInfo);
                calculation();
            }

            gatewayChange();

            $(".more-gateway-option").on("click", function(e) {
                let paymentList = $(".gateway-option-list");
                paymentList.find(".gateway-option").removeClass("d-none");
                $(this).addClass('d-none');
                paymentList.animate({
                    scrollTop: (paymentList.height() - 60)
                }, 'slow');
            });

            function calculation() {
                if (!gateway) return;
                let formattedMin = String(minAmount).replace(/[^\d.]/g, '');
                let formattedMax = String(maxAmount).replace(/[^\d.]/g, '');

                $(".gateway-limit").html("$" + formattedMin + " - $" + formattedMax);

                let percentCharge = 0;
                let fixedCharge = 0;
                let totalPercentCharge = 0;

                if (amount) {
                    percentCharge = parseFloat(gateway.percent_charge);
                    fixedCharge = parseFloat(gateway.fixed_charge);
                    totalPercentCharge = parseFloat(amount / 100 * percentCharge);
                }

                let totalCharge = parseFloat(totalPercentCharge + fixedCharge);
                let totalAmount = parseFloat((amount || 0) + totalPercentCharge + fixedCharge);

                $(".final-amount").text(totalAmount.toFixed(2));
                $(".processing-fee").text(totalCharge.toFixed(2));
                $("input[name=currency]").val(gateway.currency);
                $(".gateway-currency").text(gateway.currency);

                checkSubmitButtonStatus();

                if (gateway.currency != "{{ gs('cur_text') }}" && gateway.method.crypto != 1) {

                    $(".gateway-conversion, .conversion-currency").removeClass('d-none');
                    $(".gateway-conversion").find('.purchase-info__input .text').html(
                        `1 {{ __(gs('cur_text')) }} = <span class="rate">${parseFloat(gateway.rate).toFixed(2)}</span>  <span class="method_currency">${gateway.currency}</span>`
                    );
                    $('.in-currency').text(parseFloat(totalAmount * gateway.rate).toFixed(gateway.method.crypto == 1 ?
                        8 : 2))
                } else {
                    $(".gateway-conversion, .conversion-currency").addClass('d-none');
                }

                if (gateway.method.crypto == 1) {
                    $('.crypto-message').removeClass('d-none');
                } else {
                    $('.crypto-message').addClass('d-none');
                }
            }

            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })

            $('#acknowledgeTerms').on('change', function() {
                checkSubmitButtonStatus();
            });

            function checkSubmitButtonStatus() {
                let amountValid = amount >= Number(gateway.min_amount) && amount <= Number(gateway.max_amount);
                let termsAcknowledged = $('#acknowledgeTerms').is(':checked');

                if (amountValid && termsAcknowledged) {
                    $(".purchase-form button[type=submit]").removeAttr('disabled');
                } else {
                    $(".purchase-form button[type=submit]").attr('disabled', true);
                }
            }

            $('.gateway-input').change();

        })(jQuery);
    </script>
@endpush
