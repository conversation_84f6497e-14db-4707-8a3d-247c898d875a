<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="512" height="512" viewBox="0 0 512 512">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#627eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#627eea;stop-opacity:1" />
    </linearGradient>
  </defs>
  <g>
    <circle cx="256" cy="256" r="256" fill="url(#grad)"/>
    <g>
      <path d="M256,96 L256,212.32 L352.96,255.28 L256,96 Z" fill="white" fill-opacity="0.6"/>
      <path d="M256,96 L159.04,255.28 L256,212.32 L256,96 Z" fill="white"/>
      <path d="M256,355.28 L256,416 L353.04,271.68 L256,355.28 Z" fill="white" fill-opacity="0.6"/>
      <path d="M256,416 L256,355.28 L159.04,271.68 L256,416 Z" fill="white"/>
      <path d="M256,338.24 L352.96,255.28 L256,212.32 L256,338.24 Z" fill="white" fill-opacity="0.2"/>
      <path d="M159.04,255.28 L256,338.24 L256,212.32 L159.04,255.28 Z" fill="white" fill-opacity="0.6"/>
    </g>
  </g>
</svg>
