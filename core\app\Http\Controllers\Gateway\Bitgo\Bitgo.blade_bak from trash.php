@extends($activeTemplate . 'layouts.payment')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="card-title">@lang('BitGo Payment')</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="deposit-qr text-center">
                                <img src="https://chart.googleapis.com/chart?chs=250x250&cht=qr&chl={{ $data->address }}" alt="QR Code" class="img-thumbnail">
                            </div>
                        </div>
                        <div class="col-md-8">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>@lang('Payment Amount'):</span>
                                    <strong>{{ showAmount($data->amount, 8) }} {{ __($data->currency) }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>@lang('Wallet Address'):</span>
                                    <div class="d-flex">
                                        <span class="mr-2">{{ $data->address }}</span>
                                        <button class="btn btn-sm btn-outline-primary copy-text" data-text="{{ $data->address }}">
                                            <i class="fas fa-copy"></i> @lang('Copy Address')
                                        </button>
                                    </div>
                                </li>
                            </ul>
                            
                            <div class="alert alert-warning mt-3">
                                <p id="payment-status">@lang('Waiting for payment...')</p>
                                <p>@lang('Please send exactly') <strong>{{ showAmount($data->amount, 8) }} {{ __($data->currency) }}</strong> @lang('to the address above.')</p>
                                <p class="mb-0">@lang('The system will automatically check for payment.')</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    (function($){
        "use strict";
        
        // Copy address to clipboard
        $('.copy-text').on('click', function() {
            var text = $(this).data('text');
            var tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(text).select();
            document.execCommand('copy');
            tempInput.remove();
            
            // Show copied message
            $(this).html('<i class="fas fa-check"></i> @lang("Copied")');
            setTimeout(() => {
                $(this).html('<i class="fas fa-copy"></i> @lang("Copy Address")');
            }, 2000);
        });
        
        // Poll for transaction status
        let checkInterval = 30000; // 30 seconds
        let txCheck = setInterval(function() {
            $.ajax({
                // Use the deposit trx value for checking status
                url: "{{ route('bitgo.check', $data->trx) }}",
                method: "GET",
                success: function(response) {
                    if(response.status === 'completed') {
                        clearInterval(txCheck);
                        $('#payment-status').html('@lang("Payment confirmed! Redirecting...")');
                        $('.alert-warning').removeClass('alert-warning').addClass('alert-success');
                        setTimeout(() => {
                            window.location.href = "{{ route('user.deposit.history') }}";
                        }, 2000);
                    } else if(response.status === 'pending') {
                        $('#payment-status').html('@lang("Payment detected! Waiting for confirmations...")');
                    } else if(response.status === 'failed') {
                        clearInterval(txCheck);
                        $('#payment-status').html('@lang("Payment failed!")');
                        $('.alert-warning').removeClass('alert-warning').addClass('alert-danger');
                    } else if(response.status === 'error') {
                        $('#payment-status').html('@lang("Error checking status. Please contact support.")');
                        $('.alert-warning').removeClass('alert-warning').addClass('alert-danger');
                    }
                },
                error: function() {
                    $('#payment-status').html('@lang("Error checking status. Retrying...")');
                }
            });
        }, checkInterval);
    })(jQuery);
</script>
@endpush




