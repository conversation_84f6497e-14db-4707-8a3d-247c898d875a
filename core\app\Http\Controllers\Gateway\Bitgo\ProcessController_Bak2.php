<?php

namespace App\Http\Controllers\Gateway\Bitgo;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Gateway\PaymentController;
use App\Models\Deposit;
use App\Models\AdminNotification;
use App\Constants\Status;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ProcessController extends Controller
{
    /**
     * Process BitGo deposit
     *
     * @param Deposit $deposit
     * @return string JSON response
     */
    public static function process($deposit)
    {
        $gatewayCurrency = $deposit->gatewayCurrency();
        $bitgoAcc = json_decode($gatewayCurrency->gateway_parameter);

        // Check if we already have transaction details with an address
        if (!empty($deposit->detail)) {
            $existingDetails = json_decode($deposit->detail, true);
            if (isset($existingDetails['address']) && !empty($existingDetails['address'])) {
                // We already have an address, just return it
                Log::info('Using existing BitGo address', [
                    'transaction_id' => $deposit->trx,
                    'address' => $existingDetails['address']
                ]);

                // Create a data object for the view with the correct structure
                $send['redirect'] = true;
                $send['redirect_url'] = route('user.deposit.confirm');
                return json_encode($send);
            }
        }

        // Set environment and base URL
        $environment = $bitgoAcc->environment ?? 'test';
        $baseUrl = $environment === 'prod'
            ? 'https://app.bitgo.com/api/v2'
            : 'https://app.test.bitgo.com/api/v2';

        // Extract coin from method currency
        $coin = strtolower($deposit->method_currency);

        // Get current exchange rate and create address in a single process
        // This avoids making multiple API calls
        $result = self::createTransactionWithRate(
            $baseUrl,
            $bitgoAcc->api_key,
            $bitgoAcc->wallet_id,
            $coin,
            $deposit->method_currency,
            $deposit->trx
        );

        // If we got a rate from the API, use it and update stored rate
        if (!empty($result['rate'])) {
            $rate = $result['rate'];
            // Update the stored rate for future reference
            $gatewayCurrency->rate = $rate;
            $gatewayCurrency->save();
        } else {
            // If we couldn't get the rate, use the stored rate
            $rate = $gatewayCurrency->rate;
        }

        // Calculate the crypto amount based on the rate
        $cryptoAmount = $deposit->final_amount / $rate;

        // If address creation failed, try a direct approach to get addresses
        if (!$result['success']) {
            Log::warning('BitGo address creation failed, trying to fetch existing addresses', [
                'transaction_id' => $deposit->trx,
                'error' => $result['message']
            ]);

            try {
                // Try to get existing addresses directly
                $existingAddressesResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $bitgoAcc->api_key,
                    'Content-Type' => 'application/json',
                ])->get($baseUrl . '/' . $coin . '/wallet/' . $bitgoAcc->wallet_id . '/addresses', [
                    'limit' => 50,
                    'mine' => true
                ]);

                if ($existingAddressesResponse->successful()) {
                    $addresses = $existingAddressesResponse->json()['addresses'] ?? [];

                    // First try to find an address with our label
                    foreach ($addresses as $addr) {
                        if (isset($addr['label']) && $addr['label'] === 'Payment ' . $deposit->trx) {
                            $result = [
                                'success' => true,
                                'address' => $addr['address'],
                                'currency' => $deposit->method_currency,
                                'transaction_id' => $deposit->trx,
                                'wallet_id' => $bitgoAcc->wallet_id,
                                'rate' => $rate
                            ];
                            break;
                        }
                    }

                    // If no labeled address found, just use the first unused address
                    if (!$result['success'] && !empty($addresses)) {
                        $result = [
                            'success' => true,
                            'address' => $addresses[0]['address'],
                            'currency' => $deposit->method_currency,
                            'transaction_id' => $deposit->trx,
                            'wallet_id' => $bitgoAcc->wallet_id,
                            'rate' => $rate,
                            'note' => 'Using existing address (no label match)'
                        ];
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error fetching existing addresses', [
                    'message' => $e->getMessage()
                ]);
            }
        }

        if (!$result['success']) {
            $send['error'] = true;
            $send['message'] = $result['message'];
            return json_encode($send);
        }

        // Add the crypto amount and rate to the result
        $result['amount'] = $cryptoAmount;
        $result['rate'] = $rate;

        $deposit->detail = json_encode($result);
        $deposit->save();

        // Create a view response similar to other payment gateways
        $alias = $deposit->gateway->alias;

        // Log what we're sending to the view
        Log::info('BitGo payment data for view', [
            'result' => $result,
            'transaction_id' => $deposit->trx
        ]);

        // Create a data object for the view with the correct structure
        $send['val'] = [
            'address' => $result['address'],
            'amount' => $result['amount'],  // This is the crypto amount
            'currency' => $result['currency'],
            'transaction_id' => $deposit->trx,
            'fiat_amount' => $deposit->final_amount,  // Add the original fiat amount
            'fiat_currency' => $deposit->method_fiat ?? 'USD'  // Add fiat currency
        ];
        $send['view'] = 'user.payment.Bitgo';
        $send['method'] = 'GET';
        $send['url'] = route('user.deposit.confirm');

        return json_encode($send);
    }

    /**
     * Create a new transaction (get deposit address) and get exchange rate in one process
     *
     * @param string $baseUrl BitGo API base URL
     * @param string $apiKey BitGo API key
     * @param string $walletId BitGo wallet ID
     * @param string $coin Cryptocurrency code (lowercase)
     * @param string $currency Currency code
     * @param string $externalId External transaction ID
     * @return array Response with address, rate and other details
     */
    private static function createTransactionWithRate($baseUrl, $apiKey, $walletId, $coin, $currency, $externalId)
    {
        try {
            // Get server IP address
            $serverIp = self::getServerIp();

            // First, get the exchange rate
            $rate = null;
            try {
                // Get market data from BitGo API
                $marketResponse = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ])->get($baseUrl . '/market/latest');

                if ($marketResponse->successful()) {
                    $marketData = $marketResponse->json();

                    // Find the exchange rate for the specified crypto/fiat pair
                    if (isset($marketData['marketData'])) {
                        foreach ($marketData['marketData'] as $data) {
                            if (strtolower($data['coin']) === $coin &&
                                isset($data['currencies']) &&
                                isset($data['currencies']['USD'])) {

                                $rate = $data['currencies']['USD']['last'];

                                Log::info("BitGo exchange rate retrieved", [
                                    'crypto' => strtoupper($coin),
                                    'rate' => $rate
                                ]);

                                break;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Just log the error but continue with address creation
                Log::warning('Error getting exchange rate from BitGo', [
                    'message' => $e->getMessage()
                ]);
            }

            // Log the request details
            Log::info('BitGo creating address', [
                'wallet_id' => $walletId,
                'coin' => $coin,
                'external_id' => $externalId,
                'url' => $baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address',
                'server_ip' => $serverIp
            ]);

            // Create a new address for receiving payment
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->post($baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address', [
                'label' => 'Payment ' . $externalId,
            ]);

            // Log the full response for debugging
            Log::debug('BitGo address creation response', [
                'status' => $response->status(),
                'body' => $response->body(),
                'headers' => $response->headers(),
                'server_ip' => $serverIp
            ]);

            if (!$response->successful()) {
                // If creation failed because address already exists, try to get existing addresses
                if ($response->status() === 400 && strpos($response->body(), 'already exists') !== false) {
                    Log::info('Address already exists, fetching existing addresses', [
                        'external_id' => $externalId,
                        'server_ip' => $serverIp
                    ]);

                    // Try to get existing addresses with this label
                    $existingResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $apiKey,
                        'Content-Type' => 'application/json',
                    ])->get($baseUrl . '/' . $coin . '/wallet/' . $walletId . '/addresses', [
                        'limit' => 50,
                        'mine' => true
                    ]);

                    if ($existingResponse->successful()) {
                        $addresses = array();
                        $responseData = $existingResponse->json();
                        if (isset($responseData['addresses'])) {
                            $addresses = $responseData['addresses'];
                        }

                        // Try to find an address with our label
                        foreach ($addresses as $addr) {
                            if (isset($addr['label']) && $addr['label'] === 'Payment ' . $externalId) {
                                return [
                                    'success' => true,
                                    'address' => $addr['address'],
                                    'currency' => $currency,
                                    'transaction_id' => $externalId,
                                    'wallet_id' => $walletId,
                                    'rate' => $rate
                                ];
                            }
                        }
                    }
                }

                // Check for IP restriction error
                $errorMessage = $response->body();
                $isIpRestricted = strpos($errorMessage, 'IP-restricted token') !== false;

                Log::error('BitGo address creation failed', [
                    'response_body' => $errorMessage,
                    'status' => $response->status(),
                    'wallet_id' => $walletId,
                    'coin' => $coin,
                    'url' => $baseUrl . '/' . $coin . '/wallet/' . $walletId . '/address',
                    'server_ip' => $serverIp,
                    'is_ip_restricted' => $isIpRestricted
                ]);

                // Create admin notification for IP restriction error
                if ($isIpRestricted) {
                    $adminNotification = new AdminNotification();
                    $adminNotification->user_id = 0;
                    $adminNotification->title = 'BitGo IP Restriction Error';
                    $adminNotification->click_url = '#';
                    $adminNotification->message = "BitGo API key has IP restrictions. Server IP: {$serverIp} is not allowed. Please add this IP to the allowed list in BitGo settings.";
                    $adminNotification->save();

                    return [
                        'success' => false,
                        'message' => "BitGo API key has IP restrictions. Server IP: {$serverIp} is not allowed. Please add this IP to the allowed list in BitGo settings.",
                        'rate' => $rate
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Failed to create address: ' . ($errorMessage),
                    'rate' => $rate
                ];
            }

            $addressData = $response->json();

            return [
                'success' => true,
                'address' => $addressData['address'],
                'currency' => $currency,
                'transaction_id' => $externalId,
                'wallet_id' => $walletId,
                'rate' => $rate
            ];

        } catch (\Exception $e) {
            $serverIp = self::getServerIp();

            Log::error('BitGo API exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'server_ip' => $serverIp
            ]);

            return [
                'success' => false,
                'message' => 'API Error: ' . $e->getMessage(),
                'rate' => null
            ];
        }
    }

    /**
     * Get server IP address
     *
     * @return string Server IP address
     */
    private static function getServerIp()
    {
        try {
            // Try to get server IP from external service
            $response = Http::get('https://api.ipify.org');
            if ($response->successful()) {
                return $response->body();
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get server IP from external service', [
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to server variables
        $serverVars = ['SERVER_ADDR', 'LOCAL_ADDR'];
        foreach ($serverVars as $var) {
            if (isset($_SERVER[$var])) {
                return $_SERVER[$var];
            }
        }

        return '127.0.0.1'; // Default fallback
    }

    /**
     * Check BitGo transaction status
     * Called by AJAX from the payment page
     *
     * @param Request $request
     * @param string $trx Transaction ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkTransaction(Request $request, $trx)
    {
        Log::info('BitGo CheckTransaction: Received request.', ['trx' => $trx]);

        // Find the deposit with the given transaction ID
        $deposit = Deposit::where('trx', $trx)->orderBy('id', 'DESC')->first();

        if (!$deposit) {
            Log::warning('BitGo CheckTransaction: Deposit not found.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Transaction not found.'
            ]);
        }

        // If already processed, return success
        if ($deposit->status == Status::PAYMENT_SUCCESS) {
            return response()->json([
                'status' => true,
                'status_code' => 'completed',
                'message' => 'Payment already confirmed.',
                'confirmations' => 3
            ]);
        }

        // Only check status for initiated payments
        if ($deposit->status != Status::PAYMENT_INITIATE) {
            Log::warning('BitGo CheckTransaction: Deposit not in initiated status.', [
                'trx' => $trx,
                'status' => $deposit->status
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Transaction is not in a valid state for checking.'
            ]);
        }

        $gatewayCurrency = $deposit->gatewayCurrency();
        if (!$gatewayCurrency) {
            Log::error('BitGo CheckTransaction: Gateway currency not found.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Payment method configuration error.'
            ]);
        }

        $bitgoAcc = json_decode($gatewayCurrency->gateway_parameter);
        if (!$bitgoAcc || !isset($bitgoAcc->api_key) || !isset($bitgoAcc->wallet_id)) {
            Log::error('BitGo CheckTransaction: Invalid gateway configuration.', ['trx' => $trx]);
            return response()->json([
                'status' => false,
                'message' => 'Payment gateway configuration error.'
            ]);
        }

        $depositDetails = json_decode($deposit->detail, true);
        if (!$depositDetails || !isset($depositDetails['address'])) {
            Log::error('BitGo CheckTransaction: Invalid deposit details.', [
                'trx' => $trx,
                'details' => $deposit->detail
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Payment details incomplete. Please contact support.'
            ]);
        }

        // Extract necessary data from deposit details
        $coin = strtolower($deposit->method_currency);
        $walletId = $depositDetails['wallet_id'] ?? $bitgoAcc->wallet_id;

        // Define minimum confirmations per cryptocurrency
        $minConfirmations = [
            'btc' => 3,    // Bitcoin
            'tbtc' => 2,   // Bitcoin Testnet
            'eth' => 12,   // Ethereum
            'ltc' => 6,    // Litecoin
            'bch' => 3,    // Bitcoin Cash
            'dash' => 6,   // Dash
            'doge' => 6,   // Dogecoin
            'xrp' => 1,    // Ripple
            'xlm' => 1,    // Stellar
            'zec' => 12,   // Zcash
            'bsv' => 6,    // Bitcoin SV
            'etc' => 400,  // Ethereum Classic
            'trx' => 20,   // TRON
            'algo' => 1,   // Algorand
            'eos' => 1,    // EOS
            'hbar' => 1,   // Hedera
            'xtz' => 30,   // Tezos
            'ada' => 15,   // Cardano
            'dot' => 6,    // Polkadot
            'sol' => 32,   // Solana
            'avax' => 1,   // Avalanche
            'matic' => 128 // Polygon
        ];

        // Get required confirmations for this coin, default to 3 if not specified
        $requiredConfirmations = $minConfirmations[$coin] ?? 3;

        // Override with gateway settings if available
        if (isset($bitgoAcc->min_confirmations)) {
            $requiredConfirmations = (int)$bitgoAcc->min_confirmations;
        }

        try {
            // Use the BitgoAPI Support class to check transaction status
            $environment = $bitgoAcc->environment ?? 'test';

            // Import the BitgoAPI class
            $bitgoApiClass = '\App\Http\Controllers\Gateway\Bitgo\Support\BitgoAPI';
            if (!class_exists($bitgoApiClass)) {
                Log::error('BitGo CheckTransaction: BitgoAPI class not found.', ['trx' => $trx]);
                return response()->json([
                    'status' => false,
                    'message' => 'System configuration error.'
                ]);
            }

            $bitgoApi = new $bitgoApiClass(
                $bitgoAcc->api_key,
                $walletId,
                $environment
            );

            // Get transaction status using the existing method
            $status = $bitgoApi->getTransactionStatus(json_encode($depositDetails));

            Log::info('BitGo CheckTransaction: Status check result', [
                'trx' => $trx,
                'status' => $status,
                'coin' => $coin,
                'required_confirmations' => $requiredConfirmations
            ]);

            // Map BitgoAPI status to frontend expected status
            switch ($status) {
                case 'completed':
                    // Update deposit status in database
                    PaymentController::userDataUpdate($deposit);
                    return response()->json([
                        'status' => true,
                        'status_code' => 'completed',
                        'message' => 'Payment confirmed.',
                        'confirmations' => $requiredConfirmations
                    ]);

                case 'pending':
                    // For pending transactions, we've detected the payment but it needs more confirmations
                    return response()->json([
                        'status' => true,
                        'status_code' => 'pending',
                        'message' => 'Payment detected, awaiting confirmations.',
                        'confirmations' => 1
                    ]);

                case 'waiting':
                    // Check if payment window has expired
                    $paymentWindowMinutes = $bitgoAcc->payment_window_minutes ?? 30; // Default 30 minutes
                    if ($deposit->created_at->addMinutes($paymentWindowMinutes)->isPast()) {
                        return response()->json([
                            'status' => true,
                            'status_code' => 'expired',
                            'message' => 'Payment window has expired.'
                        ]);
                    }

                    return response()->json([
                        'status' => true,
                        'status_code' => 'waiting',
                        'message' => 'Awaiting payment.',
                        'confirmations' => 0
                    ]);

                case 'error':
                case 'unknown':
                default:
                    return response()->json([
                        'status' => false,
                        'message' => 'Error checking payment status. Please try again.'
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('BitGo CheckTransaction: Exception occurred.', [
                'trx' => $trx,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => 'An error occurred while checking payment status: ' . $e->getMessage()
            ]);
        }
    }
}

