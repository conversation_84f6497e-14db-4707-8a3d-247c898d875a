{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "authorizenet/authorizenet": "^2.0", "btcpayserver/btcpayserver-greenfield-php": "^2.3", "coingate/coingate-php": "^4.1", "ezyang/htmlpurifier": "^4.17", "google/apiclient": "^2.15", "guzzlehttp/guzzle": "^7.8", "intervention/image": "^3.6", "laramin/utility": "^1.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.6", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "mailjet/mailjet-apiv3-php": "^1.6", "messagebird/php-rest-api": "^3.1", "mollie/laravel-mollie": "^3.0", "phpmailer/phpmailer": "^6.9", "razorpay/razorpay": "^2.9", "sendgrid/sendgrid": "^8.1", "stripe/stripe-php": "^13.13", "twilio/sdk": "^7.16", "vonage/client": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Http/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}