<?php
// Direct database fix for FocLabs
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Checking General Settings table...\n";

try {
    // Check if any records exist
    $count = DB::table('general_settings')->count();
    echo "Found {$count} records in general_settings table.\n";
    
    if ($count == 0) {
        echo "Inserting minimal general settings record...\n";
        
        // Insert minimal required fields
        $result = DB::table('general_settings')->insert([
            'site_name' => 'FocLabs',
            'maintenance_mode' => 0,
            'registration' => 1,
            'active_template' => 'basic',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        if ($result) {
            echo "✅ General settings created successfully!\n";
        } else {
            echo "❌ Failed to create general settings.\n";
        }
    } else {
        echo "General settings already exist. Checking maintenance_mode...\n";
        
        $general = DB::table('general_settings')->first();
        if ($general && $general->maintenance_mode === null) {
            echo "Fixing null maintenance_mode...\n";
            DB::table('general_settings')->update(['maintenance_mode' => 0]);
            echo "✅ Maintenance mode set to 0\n";
        } else {
            echo "✅ Maintenance mode is properly set: " . ($general->maintenance_mode ?? 'null') . "\n";
        }
    }
    
    // Clear cache
    echo "Clearing cache...\n";
    DB::table('cache')->where('key', 'icolab_cache_GeneralSetting')->delete();
    echo "✅ Cache cleared!\n";
    
    // Show final status
    $general = DB::table('general_settings')->first();
    if ($general) {
        echo "\n=== Current Settings ===\n";
        echo "Site Name: " . ($general->site_name ?? 'Not set') . "\n";
        echo "Maintenance Mode: " . ($general->maintenance_mode ?? 'null') . "\n";
        echo "Registration: " . ($general->registration ?? 'null') . "\n";
        echo "Active Template: " . ($general->active_template ?? 'Not set') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\nDone! Try accessing the application now.\n";
?>
